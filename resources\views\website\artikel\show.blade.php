@extends('layouts.website')

@section('title', $article->title)

@section('content')
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <article>
                <div class="title-container">
                    <h1 class="article-title">{{ $article->title }}</h1>
                </div>
                
                <div class="mb-3 text-muted text-center">
                    <small>
                    Dipublish oleh {{ $article->author->name }} pada {{ $article->created_at->format('d M Y') }}
                    </small>
                </div>

                @if($article->image)
                    <img src="{{ Storage::url($article->image) }}" 
                         class="img-fluid rounded mb-4" 
                         alt="{{ $article->title }}">
                @endif

                <div class="article-content">
                    {!! $article->content !!}
                </div>

                <!-- Tombol Bagikan -->
                <div class="mt-4 d-flex justify-content-start align-items-center flex-wrap" style="gap: 8px;">
                    <span class="me-2 fw-bold">Bagikan:</span>
                    <!-- WhatsApp -->
                    <a href="https://wa.me/?text={{ urlencode($article->title.' '.request()->fullUrl()) }}" target="_blank" class="btn btn-success btn-sm" title="Bagikan ke WhatsApp" style="min-width: 36px;">
                        <i class="fab fa-whatsapp"></i>
                    </a>
                    <!-- Facebook -->
                    <a href="https://www.facebook.com/sharer/sharer.php?u={{ urlencode(request()->fullUrl()) }}&amp;picture={{ urlencode(Storage::url($article->image)) }}" target="_blank" class="btn btn-primary btn-sm" title="Bagikan ke Facebook" style="min-width: 36px;">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <!-- Twitter -->
                    <a href="https://twitter.com/intent/tweet?url={{ urlencode(request()->fullUrl()) }}&amp;text={{ urlencode($article->title) }}&amp;via=simaspelopor&amp;hashtags=sekolah,artikel&amp;image={{ urlencode(Storage::url($article->image)) }}" target="_blank" class="btn btn-info btn-sm" style="color:white; min-width: 36px;" title="Bagikan ke Twitter">
                        <i class="fab fa-twitter"></i>
                    </a>
                    <!-- Instagram (hanya info, tidak bisa direct share) -->
                    <a href="https://www.instagram.com/?url={{ urlencode(request()->fullUrl()) }}" target="_blank" class="btn btn-danger btn-sm" title="Bagikan ke Instagram" style="min-width: 36px;">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <!-- Telegram -->
                    <a href="https://t.me/share/url?url={{ urlencode(request()->fullUrl()) }}&amp;text={{ urlencode($article->title) }}" target="_blank" class="btn btn-secondary btn-sm" title="Bagikan ke Telegram" style="min-width: 36px;">
                        <i class="fab fa-telegram-plane"></i>
                    </a>
                    @if($article->image)
                        <img src="{{ Storage::url($article->image) }}" alt="Thumbnail" class="img-thumbnail ms-3" style="max-width:60px; max-height:60px;">
                    @endif
                </div>
            </article>
            
            <!-- Bagian Artikel Terkait -->
            <div class="related-articles mt-5">
                <h3 class="related-title">Baca Juga</h3>
                <div class="title-line"></div>
                
                <div class="row mt-4">
                    @php
                        // Ambil 3 artikel terbaru selain artikel yang sedang dibaca
                        $relatedArticles = \App\Models\Article::where('id', '!=', $article->id)
                            ->where('status', 'published')
                            ->latest()
                            ->take(3)
                            ->get();
                    @endphp
                    
                    @foreach($relatedArticles as $related)
                    <div class="col-md-4 mb-4">
                        <div class="related-card">
                            <div class="related-image">
                                <img src="{{ Storage::url($related->image) }}" alt="{{ $related->title }}">
                                <div class="related-overlay">
                                    <a href="{{ route('website.artikel.show', $related->slug) }}" class="stretched-link"></a>
                                </div>
                            </div>
                            <div class="related-body">
                                <h5 class="related-card-title">{{ Str::limit($related->title, 50) }}</h5>
                                <p class="related-date">
                                    <i class="fas fa-calendar-alt"></i> {{ $related->created_at->format('d M Y') }}
                                </p>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
                
                <div class="text-center mt-3">
                    <a href="{{ route('website.artikel') }}" class="btn btn-primary">
                        <i class="fas fa-newspaper"></i> Lihat Semua Artikel
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .title-container {
        margin-bottom: 30px;
        text-align: center;
        padding: 20px 0;
    }
    
    .article-title {
        font-size: 2.5rem;
        text-transform: uppercase;
        font-weight: 800;
        color: #1a1a1a;
        position: relative;
        display: inline-block;
        padding-bottom: 15px;
        margin-bottom: 0;
        letter-spacing: 1px;
        line-height: 1.3;
    }
    
    .article-title:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 4px;
        background: linear-gradient(to right, #007bff, #00ff88);
        border-radius: 2px;
    }
    
    .article-title:before {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 200px;
        height: 1px;
        background: #e0e0e0;
    }
    
    @media (max-width: 768px) {
        .article-title {
            font-size: 2rem;
        }
    }
    
    .article-content {
        line-height: 1.6;
    }
    
    .article-content img {
        max-width: 100%;
        height: auto;
    }
    
    /* Styling untuk Artikel Terkait */
    .related-articles {
        padding-top: 30px;
        border-top: 1px solid #e0e0e0;
    }
    
    .related-title {
        font-size: 1.8rem;
        font-weight: 700;
        color: #2c3e50;
        position: relative;
        display: inline-block;
        margin-bottom: 10px;
    }
    
    .title-line {
        width: 50px;
        height: 3px;
        background: linear-gradient(to right, #007bff, #00ff88);
        border-radius: 2px;
        margin-bottom: 20px;
    }
    
    .related-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .related-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    
    .related-image {
        position: relative;
        height: 150px;
        overflow: hidden;
    }
    
    .related-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .related-card:hover .related-image img {
        transform: scale(1.1);
    }
    
    .related-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.2);
        transition: background 0.3s ease;
    }
    
    .related-card:hover .related-overlay {
        background: rgba(0, 0, 0, 0.4);
    }
    
    .related-body {
        padding: 15px;
    }
    
    .related-card-title {
        font-size: 1rem;
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 8px;
        line-height: 1.4;
    }
    
    .related-date {
        font-size: 0.8rem;
        color: #7f8c8d;
        margin-bottom: 0;
    }
    
    @media (max-width: 768px) {
        .related-title {
            font-size: 1.5rem;
        }
    }
</style>
@endsection


