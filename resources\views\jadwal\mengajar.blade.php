
@extends('layouts.admin')

@section('title')
    <PERSON><PERSON><PERSON>jar {{ auth()->user()->name }}
@endsection

@section('content')
<div class="row">
    <!-- <PERSON><PERSON> -->
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">Da<PERSON><PERSON></h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th><PERSON>las</th>
                                <th><PERSON><PERSON></th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($jadwalList->unique('kelas_id') as $index => $jadwal)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $jadwal->kelas->nama }}</td>
                                    <td>{{ $jadwal->kelas->ta<PERSON>_ajaran }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Jad<PERSON> Mengajar -->
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h3 class="card-title">Jadwal Mengajar {{ auth()->user()->name }}</h3>
                    <div class="card-tools">
                        <div class="btn-group">
                            <button type="button" class="btn btn-secondary btn-sm dropdown-toggle" data-toggle="dropdown">
                                <i class="fas fa-filter"></i> Tahun Ajaran: {{ $tahunAjaranTerpilih }}
                            </button>
                            <div class="dropdown-menu">
                                @foreach($tahunAjaranList as $ta)
                                    <a class="dropdown-item {{ $ta == $tahunAjaranTerpilih ? 'active' : '' }}" 
                                    href="{{ route('jadwal.mengajar', ['tahun_ajaran' => $ta]) }}">
                                        {{ $ta }}
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card-body">
                @if($jadwalList->isEmpty())
                    <div class="alert alert-info">
                        Tidak ada jadwal mengajar untuk tahun ajaran ini.
                    </div>
                @else
                    @foreach($jadwalList as $jadwal)
                        <div class="card mb-4">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">{{ $jadwal->kelas->nama }}</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th width="15%">Hari</th>
                                                <th width="25%">Jam</th>
                                                <th>Mata Pelajaran</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @php
                                                $hariUrutan = [
                                                    'Senin' => 1,
                                                    'Selasa' => 2,
                                                    'Rabu' => 3,
                                                    'Kamis' => 4,
                                                    'Jumat' => 5
                                                ];
                                            @endphp
                                            @foreach($jadwal->detailJadwal->sortBy(function($detail) use ($hariUrutan) {
                                                return $hariUrutan[$detail->hari];
                                            })->groupBy('hari') as $hari => $details)
                                                @foreach($details->sortBy('waktu_mulai') as $detail)
                                                    <tr>
                                                        @if($loop->first)
                                                            <td rowspan="{{ $details->count() }}">{{ $hari }}</td>
                                                        @endif
                                                        <td>{{ substr($detail->waktu_mulai, 0, 5) }} - {{ substr($detail->waktu_selesai, 0, 5) }}</td>
                                                        <td>
                                                            @if($detail->is_istirahat || $detail->keterangan)
                                                                {{ $detail->keterangan }}
                                                            @else
                                                                {{ $detail->mataPelajaran->nama_mapel ?? 'Tidak ada mata pelajaran' }}
                                                            @endif
                                                        </td>
                                                    </tr>
                                                @endforeach
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @endif
            </div>
        </div>
    </div>
</div>
@endsection

@section('css')
<style>
    .table-responsive {
        overflow-x: auto;
    }
    .card-header.bg-primary {
        background-color: #007bff !important;
    }
</style>
@endsection
