@extends('layouts.website')

@section('title', 'Semua Event')

@section('css')
<style>
    /* Memperbaiki ukuran video YouTube agar sama dengan gambar */
    .card-img-top {
        height: 250px;
        object-fit: cover;
        width: 100%;
    }

    .embed-responsive {
        position: relative;
        display: block;
        width: 100%;
        height: 250px; /* Sama dengan tinggi gambar */
        overflow: hidden;
    }

    .embed-responsive-16by9::before {
        display: none; /* Menghapus padding default */
    }

    .embed-responsive-item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
    }

    /* Memastikan card memiliki ukuran yang sama */
    .card {
        height: 100%;
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 15px;
        overflow: hidden;
        background: #fff;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(0,0,0,0.1);
    }

    /* Memastikan container media memiliki ukuran yang sama */
    .card .embed-responsive,
    .card img.card-img-top {
        width: 100%;
        height: 250px;
        object-position: center;
    }

    .card:hover .card-img-top {
        transform: scale(1.05);
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-title {
        color: #2c3e50;
        font-weight: 700;
        font-size: 1.25rem;
        margin-bottom: 1rem;
    }

    .text-muted {
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .text-muted i {
        margin-right: 8px;
        color: #e74c3c;
    }

    .card-text {
        color: #666;
        font-size: 0.95rem;
        line-height: 1.5;
    }
</style>
@endsection

@php
/**
 * Mengekstrak ID video dari URL YouTube
 *
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url)) return null;

    $pattern =
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';

    if (preg_match($pattern, $url, $match)) {
        return $match[1];
    }

    return null;
}
@endphp

@section('content')
<div class="container py-5">
    <h1 class="mb-4">Semua Event all</h1>
    
    <div class="row">
        @foreach($events as $event)
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                @if($event->gambar)
                    <img src="{{ asset('storage/events/' . $event->gambar) }}" 
                         class="card-img-top" 
                         alt="{{ $event->judul }}">
                @elseif($event->youtube_url)
                    <div class="embed-responsive embed-responsive-16by9">
                        <iframe class="embed-responsive-item" 
                                src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}" 
                                allowfullscreen></iframe>
                    </div>
                @endif
                <div class="card-body">
                    <h5 class="card-title">{{ $event->judul }}</h5>
                    <p class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        {{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt"></i> 
                        {{ $event->lokasi }}
                    </p>
                    <p class="card-text">{{ Str::limit($event->deskripsi, 100) }}</p>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <div class="d-flex justify-content-center mt-4">
        {{ $events->links() }}
    </div>
</div>
@endsection

