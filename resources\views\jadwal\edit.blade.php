@extends('layouts.admin')

@section('title', 'Edit Jadwal Pelajaran')
 
@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Edit Jadwal Pelajaran</h3>
    </div>
    <div class="card-body">
        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <form action="{{ route('jadwal.update', $jadwal->id) }}" method="POST" id="formJadwal">
            @csrf
            @method('PUT')
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Pilih Layout</label>
                        @php
                            $user = auth()->user();
                            $isAdmin = $user->hasRole('Administrator');
                            
                            // Mapping unit_id ke layout
                            $unitToLayout = [
                                1 => 'layout_tk',
                                2 => 'layout_sd',
                                3 => 'layout_smp',
                                4 => 'layout_sma'
                            ];
                             
                            // Tentukan layout default berdasarkan unit_id user
                            $defaultLayout = isset($unitToLayout[$user->unit_id]) ? $unitToLayout[$user->unit_id] : '';
                        @endphp
                        
                        @if($isAdmin)
                            {{-- Administrator dapat memilih semua layout --}}
                            <select name="layout_type" class="form-control" required>
                                <option value="">Pilih Layout</option>
                                @foreach($layouts as $key => $value)
                                    <option value="{{ $key }}" {{ old('layout_type', $jadwal->layout_type) == $key ? 'selected' : '' }}>
                                        {{ $value }}
                                    </option>
                                @endforeach
                            </select>
                        @else
                            {{-- User non-admin hanya dapat menggunakan layout sesuai unit_id --}}
                            <select name="layout_type" class="form-control" required {{ !$isAdmin ? 'disabled' : '' }}>
                                @foreach($layouts as $key => $value)
                                    <option value="{{ $key }}" 
                                        {{ ($key == $defaultLayout) || (old('layout_type', $jadwal->layout_type) == $key) ? 'selected' : '' }}
                                        {{ $key != $defaultLayout ? 'disabled' : '' }}>
                                        {{ $value }}
                                    </option>
                                @endforeach
                            </select>
                            {{-- Tambahkan hidden input untuk memastikan nilai tetap terkirim meskipun select disabled --}}
                            <input type="hidden" name="layout_type" value="{{ old('layout_type', $jadwal->layout_type) ?? $defaultLayout }}">
                        @endif
                    </div>
                    <div class="form-group">
                        <label>Kelas</label>
                        <select name="kelas_id" class="form-control @error('kelas_id') is-invalid @enderror" required>
                            <option value="">Pilih Kelas</option>
                            @php
                                // Ambil tahun ajaran aktif
                                $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();
                                
                                // Filter kelas berdasarkan tahun ajaran aktif
                                $kelasFiltered = $kelas->filter(function($k) use ($tahunAjaranAktif) {
                                    return $k->tahun_ajaran == ($tahunAjaranAktif ? $tahunAjaranAktif->nama : null);
                                });
                            @endphp
                            @foreach($kelasFiltered as $k)
                                <option value="{{ $k->id }}" {{ old('kelas_id', $jadwal->kelas_id) == $k->id ? 'selected' : '' }}>{{ $k->nama }}</option>
                            @endforeach
                        </select>
                        @error('kelas_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Nama Kelas (Teks)</label>
                        <input type="text" name="nama_kelas_text" class="form-control @error('nama_kelas_text') is-invalid @enderror" 
                               value="{{ old('nama_kelas_text', $jadwal->nama_kelas_text) }}" required>
                        @error('nama_kelas_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Wali Kelas</label>
                        <select name="wali_kelas" class="form-control @error('wali_kelas') is-invalid @enderror" required>
                            <option value="">Pilih Wali Kelas</option>
                            @php
                                $guru = \App\Models\User::role('Guru')->get();
                            @endphp
                            @foreach($guru as $g)
                                <option value="{{ $g->name }}" {{ old('wali_kelas', $jadwal->wali_kelas) == $g->name ? 'selected' : '' }}>{{ $g->name }}</option>
                            @endforeach
                        </select>
                        @error('wali_kelas')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Tahun Ajaran</label>
                        <select name="tahun_ajaran" class="form-control @error('tahun_ajaran') is-invalid @enderror" required>
                            @foreach($tahunAjaranList as $ta)
                                <option value="{{ $ta }}" {{ old('tahun_ajaran', $jadwal->tahun_ajaran) == $ta ? 'selected' : '' }}>{{ $ta }}</option>
                            @endforeach
                        </select>
                        @error('tahun_ajaran')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div id="jadwalContainer" class="mt-4">
                <!-- Jadwal akan di-render di sini menggunakan JavaScript -->
            </div>

            <button type="submit" class="btn btn-primary mt-3">Simpan Perubahan</button>
        </form>
    </div>
</div>
@endsection

@section('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    // Definisi layouts
    const layouts = {
        layout_tk: { // TK - 10 Jam Pelajaran
            jamPelajaran: 10,
            waktu: [
                { mulai: '07:30', selesai: '08:00' },
                { mulai: '08:00', selesai: '08:30' },
                { mulai: '08:30', selesai: '09:00' },
                { mulai: '09:00', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat
                { mulai: '09:45', selesai: '10:15' },
                { mulai: '10:15', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:15' },
                { mulai: '11:15', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:15' }
            ]
        },
        layout_sd: { // SD - 14 Jam Pelajaran
            jamPelajaran: 14,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:05' },
                { mulai: '08:05', selesai: '08:40' },
                { mulai: '08:40', selesai: '09:15' },
                { mulai: '09:15', selesai: '09:45' },
                { mulai: '09:45', selesai: '10:00' }, // Istirahat 1
                { mulai: '10:00', selesai: '10:30' },
                { mulai: '10:30', selesai: '11:00' },
                { mulai: '11:00', selesai: '11:30' },
                { mulai: '11:30', selesai: '12:00' },
                { mulai: '12:00', selesai: '12:25' }, // Istirahat 2
                { mulai: '12:25', selesai: '12:55' },
                { mulai: '12:55', selesai: '13:25' },
                { mulai: '13:25', selesai: '13:55' }
            ]
        },
        layout_smp: { // SMP - 16 Jam Pelajaran
            jamPelajaran: 16,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:45' },
                { mulai: '08:45', selesai: '09:20' },
                { mulai: '09:20', selesai: '09:35' }, // Istirahat 1
                { mulai: '09:35', selesai: '10:10' },
                { mulai: '10:10', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:20' },
                { mulai: '11:20', selesai: '11:55' },
                { mulai: '11:55', selesai: '12:45' }, // Istirahat 2
                { mulai: '12:45', selesai: '13:20' },
                { mulai: '13:20', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:30' },
                { mulai: '14:30', selesai: '14:45' }, // Istirahat 3
                { mulai: '14:45', selesai: '15:20' },
                { mulai: '15:20', selesai: '15:55' }
            ]
        },
        layout_sma: { // SMA - 18 Jam Pelajaran
            jamPelajaran: 18,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:50' },
                { mulai: '08:50', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat 1
                { mulai: '09:45', selesai: '10:25' },
                { mulai: '10:25', selesai: '11:05' },
                { mulai: '11:05', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:25' },
                { mulai: '12:25', selesai: '13:15' }, // Istirahat 2
                { mulai: '13:15', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:35' },
                { mulai: '14:35', selesai: '15:15' },
                { mulai: '15:15', selesai: '15:30' }, // Istirahat 3
                { mulai: '15:30', selesai: '16:10' },
                { mulai: '16:10', selesai: '16:50' },
                { mulai: '16:50', selesai: '17:30' },
                { mulai: '17:30', selesai: '18:10' }
            ]
        }
    };
    
    // Variabel untuk menyimpan layout terakhir yang dipilih
    let lastSelectedLayout = '{{ old('layout_type', $jadwal->layout_type) }}';
    
    // Variabel untuk menyimpan seleksi saat ini
    let currentSelections = {};
    
    // Fungsi untuk menyimpan seleksi saat ini
    function saveCurrentSelections() {
        // Simpan seleksi jadwal
        $('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (value) {
                currentSelections[name] = value;
            }
        });
        
        // Simpan juga nilai form utama
        currentSelections['kelas_id'] = $('select[name="kelas_id"]').val();
        currentSelections['nama_kelas_text'] = $('input[name="nama_kelas_text"]').val();
        currentSelections['wali_kelas'] = $('select[name="wali_kelas"]').val();
        currentSelections['tahun_ajaran'] = $('select[name="tahun_ajaran"]').val();
    }
    
    // Fungsi untuk mengembalikan seleksi yang disimpan
    function restoreSelections() {
        for (const name in currentSelections) {
            $(`select[name="${name}"]`).val(currentSelections[name]);
            $(`input[name="${name}"]`).val(currentSelections[name]);
        }
    }
    
    // Fungsi untuk setup handler perubahan mapel
    function setupMapelChangeHandler() {
        $('select[name$="[mapel_id]"]').change(function() {
            checkBentrok($(this));
        });
    }
    
    // Fungsi untuk render jadwal berdasarkan layout yang dipilih
    function renderJadwalTable(layoutType) {
        console.log("Rendering jadwal table for layout:", layoutType);
        
        if (!layoutType) {
            console.error("Layout type tidak valid");
            return;
        }
        
        // Simpan seleksi saat ini sebelum render ulang
        saveCurrentSelections();
        
        const layout = layouts[layoutType];
        if (!layout) {
            console.error("Layout tidak ditemukan untuk type:", layoutType);
            return;
        }
        
        console.log("Layout ditemukan:", layout);
        
        // Buat HTML untuk tabel jadwal
        let html = `
            <h4>Jadwal Pelajaran</h4>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Hari</th>
                        <th>Jam</th>
                        <th>Mata Pelajaran</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        hari.forEach((h, hIndex) => {
            layout.waktu.forEach((w, wIndex) => {
                const selectName = `jadwal[${hIndex}_${wIndex}][mapel_id]`;
                
                html += `
                    <tr>
                        ${wIndex === 0 ? `<td rowspan="${layout.waktu.length}">${h}</td>` : ''}
                        <td>${w.mulai}-${w.selesai}</td>
                        <td>
                            <select name="${selectName}" class="form-control" data-hari="${h}" data-waktu-mulai="${w.mulai}" data-waktu-selesai="${w.selesai}">
                                <option value="">-- Kosong --</option>
                                <optgroup label="Mata Pelajaran">
                                    @foreach($mataPelajaran as $mp)
                                        <option value="{{ $mp->id }}">{{ $mp->nama_mapel }} ({{ $mp->pengajar->name ?? 'Belum ditentukan' }})</option>
                                    @endforeach
                                </optgroup>
                                <optgroup label="Kegiatan Khusus">
                                    @foreach($specialEvents as $event)
                                        <option value="{{ $event['id'] }}">{{ $event['nama'] }}</option>
                                    @endforeach
                                </optgroup>
                            </select>
                            <input type="hidden" name="jadwal[${hIndex}_${wIndex}][hari]" value="${h}">
                            <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_mulai]" value="${w.mulai}">
                            <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_selesai]" value="${w.selesai}">
                        </td>
                    </tr>
                `;
            });
        });
        
        html += `
                </tbody>
            </table>
        `;
        
        console.log("Rendering HTML to jadwalContainer");
        $('#jadwalContainer').html(html);
        
        // Isi jadwal dari data yang ada
        console.log("Calling fillJadwalFromData");
        fillJadwalFromData();
        
        // Kembalikan seleksi yang disimpan
        console.log("Restoring selections");
        restoreSelections();
        
        // Setup handler untuk perubahan mapel
        console.log("Setting up mapel change handler");
        setupMapelChangeHandler();
        
        // Initialize Select2 for mata pelajaran dropdowns
        $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
            theme: 'bootstrap4',
            placeholder: "Pilih Mata Pelajaran",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#jadwalContainer'),
            language: {
                searching: function() {
                    return "Ketik nama guru atau mata pelajaran...";
                }
            }
        });
    }
    
    // Fungsi untuk mengisi jadwal dari data yang ada
    function fillJadwalFromData() {
        console.log("Filling jadwal data...");
        
        // Data detail jadwal dari database
        const detailJadwal = @json($jadwal->detailJadwal);
        console.log("Detail jadwal:", detailJadwal);
        
        if (!detailJadwal || detailJadwal.length === 0) {
            console.log("Tidak ada detail jadwal yang ditemukan");
            return;
        }
        
        // Kelompokkan detail jadwal berdasarkan hari
        const jadwalByHari = {};
        ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'].forEach(hari => {
            jadwalByHari[hari] = detailJadwal
                .filter(detail => detail.hari === hari)
                .sort((a, b) => {
                    // Urutkan berdasarkan waktu mulai
                    return a.waktu_mulai.localeCompare(b.waktu_mulai);
                });
        });
        
        console.log("Jadwal grouped by hari:", jadwalByHari);
        
        // Iterasi setiap hari
        ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'].forEach((hari, hariIndex) => {
            const detailsForDay = jadwalByHari[hari] || [];
            
            // Dapatkan semua select elements untuk hari ini
            const selects = $(`select[data-hari="${hari}"]`);
            console.log(`Found ${selects.length} selects for day ${hari}`);
            
            // Isi select elements berdasarkan urutan
            detailsForDay.forEach((detail, index) => {
                if (index < selects.length) {
                    const selectElement = selects.eq(index);
                    const selectName = selectElement.attr('name');
                    let value = '';
                    
                    // Tentukan nilai yang akan diset
                    if (detail.is_istirahat) {
                        value = 'istirahat';
                    } else if (detail.keterangan) {
                        // Cari special event yang sesuai
                        const keterangan = detail.keterangan.toLowerCase();
                        if (keterangan === 'upacara') {
                            value = 'upacara';
                        } else if (keterangan === 'kebaktian') {
                            value = 'kebaktian';
                        } else if (keterangan === 'istirahat') {
                            value = 'istirahat';
                        } else if (keterangan === 'ekstra') {
                            value = 'ekstra';
                        }
                    } else if (detail.mata_pelajaran_id) {
                        value = detail.mata_pelajaran_id;
                    }
                    
                    console.log(`Setting select at index ${index} (${selectName}) to value ${value}`);
                    
                    // Set nilai pada elemen select
                    if (value) {
                        selectElement.val(value);
                        console.log(`Value set successfully for ${selectName}`);
                    }
                }
            });
        });
    }
    
    // Handler untuk perubahan layout
    $('select[name="layout_type"]').change(function() {
        const layoutType = $(this).val();
        renderJadwalTable(layoutType);
        lastSelectedLayout = layoutType;
    });
    
    // Inisialisasi jadwal saat halaman dimuat
    // Ambil layout dari data jadwal yang ada
    const currentLayout = '{{ old('layout_type', $jadwal->layout_type) }}';
    
    // Untuk user non-admin, ambil nilai default layout dari hidden input
    const defaultLayout = $('input[type="hidden"][name="layout_type"]').val() || $('select[name="layout_type"]').val() || currentLayout;
    
    console.log("Current layout:", currentLayout);
    console.log("Default layout:", defaultLayout);
    
    // Render jadwal dengan layout yang tersedia
    if (defaultLayout && layouts[defaultLayout]) {
        console.log("Rendering layout:", defaultLayout);
        renderJadwalTable(defaultLayout);
        lastSelectedLayout = defaultLayout;
        
        // Set nilai select layout jika ada
        if ($('select[name="layout_type"]').length) {
            $('select[name="layout_type"]').val(defaultLayout);
        }
    } else {
        // Jika tidak ada layout yang valid, gunakan layout pertama yang tersedia
        const firstLayout = Object.keys(layouts)[0];
        if (firstLayout) {
            console.log("Using first available layout:", firstLayout);
            renderJadwalTable(firstLayout);
            lastSelectedLayout = firstLayout;
            
            // Set nilai select layout
            if ($('select[name="layout_type"]').length) {
                $('select[name="layout_type"]').val(firstLayout);
            }
        }
    }
    
    // Tambahkan event handler untuk menyimpan nilai form utama saat berubah
    $('select[name="kelas_id"], input[name="nama_kelas_text"], select[name="wali_kelas"], select[name="tahun_ajaran"]').change(function() {
        saveCurrentSelections();
    });
    
    // Initialize Select2 for other dropdowns
    $('select[name="kelas_id"], select[name="wali_kelas"], select[name="tahun_ajaran"]').select2({
        theme: 'bootstrap4',
        width: '100%'
    });
    
    // Tambahkan validasi form
    $('#formJadwal').submit(function(e) {
        e.preventDefault(); // Prevent default submission

        // Validasi yang sudah ada
        const jadwalPerHari = {};
        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        hari.forEach(h => jadwalPerHari[h] = 0);

        let error = false;
        let bentrokFound = false;

        // Cek jumlah mapel per hari
        $(this).find('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const hari = $(`input[name="${name.replace('[mapel_id]', '[hari]')}"]`).val();
            const value = $(this).val();
            if (value && !value.includes('istirahat')) {
                jadwalPerHari[hari]++;
            }

            // Cek bentrok untuk setiap mapel
            if (checkBentrok($(this))) {
                bentrokFound = true;
            }
        });

        // Validasi minimal 1 mapel per hari
        hari.forEach(h => {
            if (jadwalPerHari[h] < 1) {
                Swal.fire({
                    title: 'Error!',
                    text: `Hari ${h} harus memiliki minimal 1 mata pelajaran`,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                error = true;
            }
        });

        if (!error && !bentrokFound) {
            // Jika tidak ada error dan bentrok, submit form
            this.submit();
        }
    });
});

function checkBentrok(changedSelect) {
    const selectedMapelId = changedSelect.val();
    if (!selectedMapelId || selectedMapelId.includes('istirahat') || 
        selectedMapelId.includes('upacara') || selectedMapelId.includes('kebaktian')) {
        return false;
    }

    const hari = changedSelect.data('hari');
    const waktuMulai = changedSelect.data('waktu-mulai');
    const waktuSelesai = changedSelect.data('waktu-selesai');
    let bentrok = false;

    // Cek bentrok dengan jadwal lain pada hari yang sama
    $(`select[data-hari="${hari}"]`).not(changedSelect).each(function() {
        const otherMapelId = $(this).val();
        if (otherMapelId && otherMapelId === selectedMapelId && 
            !otherMapelId.includes('istirahat') && !otherMapelId.includes('upacara') && 
            !otherMapelId.includes('kebaktian')) {
            
            const otherWaktuMulai = $(this).data('waktu-mulai');
            const otherWaktuSelesai = $(this).data('waktu-selesai');
            
            if (isTimeOverlap(waktuMulai, waktuSelesai, otherWaktuMulai, otherWaktuSelesai)) {
                Swal.fire({
                    title: 'Peringatan!',
                    text: `Terjadi bentrok jadwal untuk mata pelajaran yang sama pada hari ${hari}`,
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                bentrok = true;
                return false; // break the each loop
            }
        }
    });
    
    return bentrok;
}

function isTimeOverlap(start1, end1, start2, end2) {
    return (start1 < end2 && end1 > start2);
}
</script>
@endsection


