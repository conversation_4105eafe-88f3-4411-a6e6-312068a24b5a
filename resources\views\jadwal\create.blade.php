@extends('layouts.admin')

@section('title', 'Buat Jadwal Pelajaran')
 
@section('content')
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Buat Jadwal Pelajaran Baru</h3>
    </div>
    <div class="card-body">
        @if(session('error'))
            <div class="alert alert-danger">{{ session('error') }}</div>
        @endif

        <form action="{{ route('jadwal.store') }}" method="POST" id="formJadwal">
            @csrf
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label>Pilih Layout</label>
                        @php
                            $user = auth()->user();
                            $isAdmin = $user->hasRole('Administrator');
                            
                            // Mapping unit_id ke layout
                            $unitToLayout = [
                                1 => 'layout_tk',
                                2 => 'layout_sd',
                                3 => 'layout_smp',
                                4 => 'layout_sma'
                            ];
                            
                            // Tentukan layout default berdasarkan unit_id user
                            $defaultLayout = isset($unitToLayout[$user->unit_id]) ? $unitToLayout[$user->unit_id] : '';
                        @endphp
                        
                        @if($isAdmin)
                            {{-- Administrator dapat memilih semua layout --}}
                            <select name="layout_type" class="form-control" required>
                                <option value="">Pilih Layout</option>
                                @foreach($layouts as $key => $value)
                                    <option value="{{ $key }}" {{ old('layout_type') == $key ? 'selected' : '' }}>
                                        {{ $value }}
                                    </option>
                                @endforeach
                            </select>
                        @else
                            {{-- User non-admin hanya dapat menggunakan layout sesuai unit_id --}}
                            <select name="layout_type" class="form-control" required {{ !$isAdmin ? 'disabled' : '' }}>
                                @foreach($layouts as $key => $value)
                                    <option value="{{ $key }}" 
                                        {{ $key == $defaultLayout ? 'selected' : '' }}
                                        {{ $key != $defaultLayout ? 'disabled' : '' }}>
                                        {{ $value }}
                                    </option>
                                @endforeach
                            </select>
                            {{-- Tambahkan hidden input untuk memastikan nilai tetap terkirim meskipun select disabled --}}
                            <input type="hidden" name="layout_type" value="{{ $defaultLayout }}">
                        @endif
                    </div>
                    <div class="form-group">
                        <label>Kelas</label>
                        <select name="kelas_id" class="form-control @error('kelas_id') is-invalid @enderror" required>
                            <option value="">Pilih Kelas</option>
                            @php
                                // Ambil tahun ajaran aktif
                                $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();
                                
                                // Filter kelas berdasarkan tahun ajaran aktif
                                $kelasFiltered = $kelas->filter(function($k) use ($tahunAjaranAktif) {
                                    return $k->tahun_ajaran == ($tahunAjaranAktif ? $tahunAjaranAktif->nama : null);
                                });
                            @endphp
                            @foreach($kelasFiltered as $k)
                                <option value="{{ $k->id }}" {{ old('kelas_id') == $k->id ? 'selected' : '' }}>{{ $k->nama }}</option>
                            @endforeach
                        </select>
                        @error('kelas_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Nama Kelas (Teks)</label>
                        <input type="text" name="nama_kelas_text" class="form-control @error('nama_kelas_text') is-invalid @enderror" value="{{ old('nama_kelas_text') }}" required>
                        @error('nama_kelas_text')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Wali Kelas</label>
                        <select name="wali_kelas" class="form-control @error('wali_kelas') is-invalid @enderror" required>
                            <option value="">Pilih Wali Kelas</option>
                            @php
                                // Ambil tahun ajaran aktif
                                $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();
                                
                                // Ambil data guru dengan tugas tambahan Wali Kelas atau Guru Kelas
                                // pada tahun ajaran yang aktif
                                $waliKelas = DB::table('guru_tugastambahan')
                                    ->whereIn('tugas_tambahan', ['Wali Kelas', 'Guru Kelas'])
                                    ->where('tahun_ajaran_id', $tahunAjaranAktif ? $tahunAjaranAktif->id : null)
                                    ->orderBy('nama_guru')
                                    ->get();
                            @endphp
                            @foreach($waliKelas as $wali)
                                <option value="{{ $wali->nama_guru }}" {{ old('wali_kelas') == $wali->nama_guru ? 'selected' : '' }}>{{ $wali->nama_guru }}</option>
                            @endforeach
                        </select>
                        @error('wali_kelas')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    <div class="form-group">
                        <label>Tahun Ajaran</label>
                        <select name="tahun_ajaran" class="form-control @error('tahun_ajaran') is-invalid @enderror" required>
                            <option value="">Pilih Tahun Ajaran</option>
                            @foreach($tahunAjaranList as $tahun)
                                <option value="{{ $tahun }}" {{ old('tahun_ajaran') == $tahun ? 'selected' : '' }}>{{ $tahun }}</option>
                            @endforeach
                        </select>
                        @error('tahun_ajaran')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                </div>
            </div>

            <div id="jadwalContainer" class="mt-4">
                <!-- Jadwal akan di-render di sini menggunakan JavaScript -->
            </div>

            <button type="submit" class="btn btn-primary mt-3">Simpan Jadwal</button>
        </form>
    </div>
</div>
@endsection

@section('css')
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
@stop

@section('js')
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    const layouts = {
        layout_tk: { // TK - 10 Jam Pelajaran
            jamPelajaran: 10,
            waktu: [
                { mulai: '07:30', selesai: '08:00' },
                { mulai: '08:00', selesai: '08:30' },
                { mulai: '08:30', selesai: '09:00' },
                { mulai: '09:00', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat
                { mulai: '09:45', selesai: '10:15' },
                { mulai: '10:15', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:15' },
                { mulai: '11:15', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:15' }
            ]
        },
        layout_sd: { // SD - 14 Jam Pelajaran
            jamPelajaran: 14,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:05' },
                { mulai: '08:05', selesai: '08:40' },
                { mulai: '08:40', selesai: '09:15' },
                { mulai: '09:15', selesai: '09:45' },
                { mulai: '09:45', selesai: '10:00' }, // Istirahat 1
                { mulai: '10:00', selesai: '10:30' },
                { mulai: '10:30', selesai: '11:00' },
                { mulai: '11:00', selesai: '11:30' },
                { mulai: '11:30', selesai: '12:00' },
                { mulai: '12:00', selesai: '12:25' }, // Istirahat 2
                { mulai: '12:25', selesai: '12:55' },
                { mulai: '12:55', selesai: '13:25' },
                { mulai: '13:25', selesai: '13:55' }
            ]
        },
        layout_smp: { // SMP - 16 Jam Pelajaran
            jamPelajaran: 16,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:45' },
                { mulai: '08:45', selesai: '09:20' },
                { mulai: '09:20', selesai: '09:35' }, // Istirahat 1
                { mulai: '09:35', selesai: '10:10' },
                { mulai: '10:10', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:20' },
                { mulai: '11:20', selesai: '11:55' },
                { mulai: '11:55', selesai: '12:45' }, // Istirahat 2
                { mulai: '12:45', selesai: '13:20' },
                { mulai: '13:20', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:30' },
                { mulai: '14:30', selesai: '14:45' }, // Istirahat 3
                { mulai: '14:45', selesai: '15:20' },
                { mulai: '15:20', selesai: '15:55' }
            ]
        },
        layout_sma: { // SMA - 18 Jam Pelajaran
            jamPelajaran: 18,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:50' },
                { mulai: '08:50', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat 1
                { mulai: '09:45', selesai: '10:25' },
                { mulai: '10:25', selesai: '11:05' },
                { mulai: '11:05', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:25' },
                { mulai: '12:25', selesai: '13:15' }, // Istirahat 2
                { mulai: '13:15', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:35' },
                { mulai: '14:35', selesai: '15:15' },
                { mulai: '15:15', selesai: '15:30' }, // Istirahat 3
                { mulai: '15:30', selesai: '16:10' },
                { mulai: '16:10', selesai: '16:50' },
                { mulai: '16:50', selesai: '17:30' },
                { mulai: '17:30', selesai: '18:10' }
            ]
        }
    };

    // Variabel untuk menyimpan layout terakhir yang dipilih
    let lastSelectedLayout = '';
    
    // Variabel untuk menyimpan seleksi saat ini
    let savedSelections = {};
    
    // Fungsi untuk menyimpan seleksi saat ini
    function saveCurrentSelections() {
        // Simpan seleksi jadwal
        $('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (value) {
                savedSelections[name] = value;
            }
        });
        
        // Simpan juga nilai form utama
        savedSelections['kelas_id'] = $('select[name="kelas_id"]').val();
        savedSelections['nama_kelas_text'] = $('input[name="nama_kelas_text"]').val();
        savedSelections['wali_kelas'] = $('select[name="wali_kelas"]').val();
        savedSelections['tahun_ajaran'] = $('select[name="tahun_ajaran"]').val();
    }
    
    // Fungsi untuk mengembalikan seleksi yang disimpan
    function restoreSelections() {
        // Kembalikan seleksi jadwal
        for (const name in savedSelections) {
            if (name.includes('[mapel_id]')) {
                $(`select[name="${name}"]`).val(savedSelections[name]);
            }
        }
        
        // Kembalikan nilai form utama jika ada
        if (savedSelections['kelas_id']) $('select[name="kelas_id"]').val(savedSelections['kelas_id']);
        if (savedSelections['nama_kelas_text']) $('input[name="nama_kelas_text"]').val(savedSelections['nama_kelas_text']);
        if (savedSelections['wali_kelas']) $('select[name="wali_kelas"]').val(savedSelections['wali_kelas']);
        if (savedSelections['tahun_ajaran']) $('select[name="tahun_ajaran"]').val(savedSelections['tahun_ajaran']);
    }
    
    // Fungsi untuk render jadwal berdasarkan layout yang dipilih
    function renderJadwalTable(layoutType) {
        if (!layoutType) return;
        
        // Simpan seleksi saat ini sebelum render ulang
        saveCurrentSelections();
        
        const layout = layouts[layoutType];
        if (!layout) return;
        
        let html = `
            <h4>Jadwal Pelajaran</h4>
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th>Hari</th>
                        <th>Jam</th>
                        <th>Mata Pelajaran</th>
                    </tr>
                </thead>
                <tbody>
        `;
        
        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        hari.forEach((h, hIndex) => {
            layout.waktu.forEach((w, wIndex) => {
                const selectName = `jadwal[${hIndex}_${wIndex}][mapel_id]`;
                
                html += `
                    <tr>
                        ${wIndex === 0 ? `<td rowspan="${layout.waktu.length}">${h}</td>` : ''}
                        <td>${w.mulai}-${w.selesai}</td>
                        <td>
                            <select name="${selectName}" class="form-control" data-hari="${h}" data-waktu-mulai="${w.mulai}" data-waktu-selesai="${w.selesai}">
                                <option value="">-- Kosong --</option>
                                <optgroup label="Mata Pelajaran">
                                    @foreach($mataPelajaran as $mp)
                                        <option value="{{ $mp->id }}">{{ $mp->nama_mapel }} ({{ $mp->pengajar->name }})</option>
                                    @endforeach
                                </optgroup>
                                <optgroup label="Kegiatan Khusus">
                                    @foreach($specialEvents as $event)
                                        <option value="{{ $event['id'] }}">{{ $event['nama'] }}</option>
                                    @endforeach
                                </optgroup>
                            </select>
                            <input type="hidden" name="jadwal[${hIndex}_${wIndex}][hari]" value="${h}">
                            <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_mulai]" value="${w.mulai}">
                            <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_selesai]" value="${w.selesai}">
                        </td>
                    </tr>
                `;
            });
        });
         
        html += `
                </tbody>
            </table>
        `;
        
        $('#jadwalContainer').html(html);
        
        // Kembalikan seleksi yang disimpan
        restoreSelections();
        
        // Setup handler untuk perubahan mapel
        setupMapelChangeHandler();
        
        // Initialize Select2 for mata pelajaran dropdowns
        $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
            theme: 'bootstrap4',
            placeholder: "Pilih Mata Pelajaran",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#jadwalContainer'),
            language: {
                searching: function() {
                    return "Ketik nama guru atau mata pelajaran...";
                }
            }
        });
    }
    
    // Handler untuk perubahan layout
    $('select[name="layout_type"]').change(function() {
        const layoutType = $(this).val();
        renderJadwalTable(layoutType);
        lastSelectedLayout = layoutType;
    });
    
    // Untuk user non-admin, ambil nilai default layout dari hidden input
    const defaultLayout = $('input[name="layout_type"]').val() || $('select[name="layout_type"]').val();
    if (defaultLayout) {
        // Render jadwal dengan layout default
        renderJadwalTable(defaultLayout);
        lastSelectedLayout = defaultLayout;
    } else {
        // Trigger change event untuk admin yang harus memilih layout
        $('select[name="layout_type"]').trigger('change');
    }
    
    // Tambahkan event handler untuk menyimpan nilai form utama saat berubah
    $('select[name="kelas_id"], input[name="nama_kelas_text"], select[name="wali_kelas"], select[name="tahun_ajaran"]').change(function() {
        saveCurrentSelections();
    });
    
    // Tambahkan validasi form
    $('#formJadwal').submit(function(e) {
        e.preventDefault(); // Prevent default submission

        // Validasi yang sudah ada
        const jadwalPerHari = {};
        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        hari.forEach(h => jadwalPerHari[h] = 0);

        let error = false;
        let bentrokFound = false;

        // Cek jumlah mapel per hari
        $(this).find('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const hari = $(`input[name="${name.replace('[mapel_id]', '[hari]')}"]`).val();
            const value = $(this).val();
            if (value && !value.includes('istirahat')) {
                jadwalPerHari[hari]++;
            }

            // Cek bentrok untuk setiap mapel
            if (checkBentrok($(this))) {
                bentrokFound = true;
            }
        });

        // Validasi minimal 1 mapel per hari
        hari.forEach(h => {
            if (jadwalPerHari[h] < 1) {
                Swal.fire({
                    title: 'Error!',
                    text: `Hari ${h} harus memiliki minimal 1 mata pelajaran`,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                error = true;
            }
        });

        if (!error && !bentrokFound) {
            // Jika tidak ada error dan bentrok, submit form
            this.submit();
        }
    });
});

function checkBentrok(changedSelect) {
    const selectedMapelId = changedSelect.val();
    if (!selectedMapelId || selectedMapelId.includes('istirahat') || 
        selectedMapelId.includes('upacara') || selectedMapelId.includes('kebaktian')) {
        return false;
    }

    const hari = changedSelect.data('hari');
    const waktuMulai = changedSelect.data('waktu-mulai');
    const waktuSelesai = changedSelect.data('waktu-selesai');
    let bentrok = false;

    $('select[name$="[mapel_id]"]').each(function() {
        const $otherSelect = $(this);
        if ($otherSelect.attr('name') !== changedSelect.attr('name') && 
            $otherSelect.val() === selectedMapelId &&
            $otherSelect.data('hari') === hari) {
            
            const otherMulai = $otherSelect.data('waktu-mulai');
            const otherSelesai = $otherSelect.data('waktu-selesai');

            if (isTimeOverlap(waktuMulai, waktuSelesai, otherMulai, otherSelesai)) {
                const message = `Peringatan: Terdapat bentrok jadwal guru pada hari ${hari} antara jam ${waktuMulai}-${waktuSelesai} dengan jam ${otherMulai}-${otherSelesai}`;
                Swal.fire({
                    title: 'Bentrok Terdeteksi!',
                    text: message,
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                bentrok = true;
            }
        }
    });
    return bentrok;
}

function isTimeOverlap(start1, end1, start2, end2) {
    return (start1 < end2 && end1 > start2);
}

// Fungsi untuk setup event handler pada select mapel
function setupMapelChangeHandler() {
    $('select[name$="[mapel_id]"]').change(function() {
        checkBentrok($(this));
    });
}

// Jika ada old input dari controller, isi kembali form
@if(old('jadwal'))
    $(document).ready(function() {
        // Tunggu sampai jadwal container dirender
        setTimeout(function() {
            @foreach(old('jadwal') as $key => $detail)
                @if(isset($detail['mapel_id']) && $detail['mapel_id'])
                    $('select[name="jadwal[{{ $key }}][mapel_id]"]').val('{{ $detail['mapel_id'] }}');
                @endif
            @endforeach
        }, 500);
    });
@endif

    // Initialize Select2 for mata pelajaran dropdowns
    $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
        theme: 'bootstrap4',
        placeholder: "Pilih Mata Pelajaran",
        allowClear: true,
        width: '100%',
        dropdownParent: $('#jadwalContainer'),
        language: {
            searching: function() {
                return "Ketik nama guru atau mata pelajaran...";
            }
        }
    });
    
    // Re-initialize Select2 when jadwal is rendered
    $('select[name="layout_type"]').on('change', function() {
        // Wait for the jadwal to be rendered
        setTimeout(function() {
            $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
                theme: 'bootstrap4',
                placeholder: "Pilih Mata Pelajaran",
                allowClear: true,
                width: '100%',
                dropdownParent: $('#jadwalContainer'),
                language: {
                    searching: function() {
                        return "Ketik nama guru atau mata pelajaran...";
                    }
                }
            });
        }, 500);
    });
</script>
@endsection
