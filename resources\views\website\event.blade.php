@extends('layouts.website')

@section('title', 'Event')

@section('css')
<style>
    /* Memperbaiki ukuran video YouTube agar sama dengan gambar */
    .card-img-top {
        height: 250px;
        object-fit: cover;
        width: 100%;
    }

    .embed-responsive {
        position: relative;
        display: block;
        width: 100%;
        height: 250px; /* Sama dengan tinggi gambar */
        padding: 0 !important; /* Menghapus padding */
        overflow: hidden;
    }

    .embed-responsive-16by9::before {
        display: none !important; /* Menghapus padding default */
    }

    .embed-responsive-item {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border: 0;
    }

    /* Memastikan card memiliki ukuran yang sama */
    .card {
        height: 100%;
    }

    /* Memastikan container media memiliki ukuran yang sama */
    .card .embed-responsive,
    .card img.card-img-top {
        width: 100%;
        height: 250px;
        object-position: center;
    }
    
    /* Memastikan container media YouTube memiliki ukuran penuh */
    .media-container {
        width: 100%;
        height: 250px;
        overflow: hidden;
    }
    
    .media-container .embed-responsive {
        width: 100%;
        height: 100%;
    }
    
    /* Memperbaiki tampilan YouTube agar memenuhi seluruh kotak */
    .embed-responsive iframe.embed-responsive-item {
        width: 100% !important;
        height: 100% !important;
        left: 0 !important;
        right: 0 !important;
        top: 0 !important;
        bottom: 0 !important;
        position: absolute !important;
        object-fit: cover !important;
    }
    
    /* Mengatasi masalah tampilan pada browser tertentu */
    iframe {
        width: 100% !important;
        height: 100% !important;
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
    }
</style>
@endsection

@php
/**
 * Mengekstrak ID video dari URL YouTube
 * 
 * @param string $url URL YouTube
 * @return string|null ID video YouTube atau null jika tidak valid
 */
function getYoutubeVideoId($url) {
    if (empty($url)) return null;
    
    $pattern = 
        '/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/i';
    
    if (preg_match($pattern, $url, $match)) {
        return $match[1];
    }
    
    return null;
}
@endphp

@section('content')
<div class="container py-5">
    <div class="title-container mb-5">
        <div class="text-center">
            <h1 class="event-title">Event Sekolah</h1>
            <div class="title-decoration"></div>
        </div>
    </div>
    
    <div class="row">
        @foreach($events as $event)
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="media-container">
                    @if($event->gambar)
                        <img src="{{ asset('storage/events/' . $event->gambar) }}" 
                             class="card-img-top" 
                             alt="{{ $event->judul }}">
                    @elseif($event->youtube_url)
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item" 
                                    src="https://www.youtube.com/embed/{{ getYoutubeVideoId($event->youtube_url) }}?rel=0" 
                                    frameborder="0"
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                    allowfullscreen></iframe>
                        </div>
                    @endif
                </div>
                <div class="card-body">
                    <h5 class="card-title">{{ $event->judul }}</h5>
                    <p class="text-muted">
                        <i class="fas fa-calendar"></i> 
                        {{ \Carbon\Carbon::parse($event->tanggal)->format('d M Y') }}
                    </p>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt"></i> 
                        {{ $event->lokasi }}
                    </p>
                    <p class="card-text">{{ Str::limit($event->deskripsi, 100) }}</p>
                </div>
            </div>
        </div>
        @endforeach
    </div>

    <div class="d-flex justify-content-center mt-4">
        {{ $events->links() }}
    </div>
</div>

<!-- style jangan dihapus -->
<style>
.title-container {
    position: relative;
    padding: 40px 0;
    text-align: center;
    margin-bottom: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.event-title {
    font-size: 2.8rem;
    font-weight: 800;
    color: #2c3e50;
    text-transform: uppercase;
    letter-spacing: 3px;
    margin: 0 auto;
    padding-bottom: 20px;
    position: relative;
    display: inline-block;
}

.event-title::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 4px;
    background: linear-gradient(to right, #e74c3c, #f39c12);
    border-radius: 2px;
}

.event-title::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 16px;
    height: 16px;
    background-color: #fff;
    border: 3px solid #e74c3c;
    border-radius: 50%;
}

.title-container::before,
.title-container::after {
    content: '•';
    font-size: 2rem;
    color: #e74c3c;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.title-container::before {
    left: 20%;
}

.title-container::after {
    right: 20%;
}

/* Tambahan dekorasi */
.title-container .text-center::before,
.title-container .text-center::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 100px;
    height: 2px;
    background: linear-gradient(to right, transparent, #e74c3c, transparent);
}

.title-container .text-center::before {
    right: 65%;
}

.title-container .text-center::after {
    left: 65%;
}

/* Card styling */
.card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: none;
    border-radius: 15px;
    overflow: hidden;
    background: #fff;
    box-shadow: 0 5px 15px rgba(0,0,0,0.05);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.1);
}

.card-img-top {
    height: 250px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.card:hover .card-img-top {
    transform: scale(1.05);
}

.card-body {
    padding: 1.5rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 700;
    font-size: 1.25rem;
    margin-bottom: 1rem;
}

.text-muted {
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.text-muted i {
    margin-right: 8px;
    color: #e74c3c;
}

.card-text {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.5;
}

/* Responsive styling */
@media (max-width: 768px) {
    .event-title {
        font-size: 2rem;
    }
    
    .title-container::before {
        left: 5%;
    }
    
    .title-container::after {
        right: 5%;
    }
    
    .title-container .text-center::before,
    .title-container .text-center::after {
        width: 50px;
    }
    
    .title-container .text-center::before {
        right: 75%;
    }
    
    .title-container .text-center::after {
        left: 75%;
    }
}

/* Pagination styling */
.pagination {
    margin-top: 2rem;
}

.page-link {
    color: #e74c3c;
    border: none;
    padding: 0.5rem 1rem;
    margin: 0 3px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.page-link:hover {
    background-color: #e74c3c;
    color: #fff;
}

.page-item.active .page-link {
    background-color: #e74c3c;
    border-color: #e74c3c;
}
</style>
@endsection






