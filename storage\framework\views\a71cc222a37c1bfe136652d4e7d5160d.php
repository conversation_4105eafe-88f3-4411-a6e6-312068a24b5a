

<?php $__env->startSection('title', 'Buat Jadwal Pelajaran'); ?>
 
<?php $__env->startSection('content'); ?>
<div class="card">
    <div class="card-header">
        <h3 class="card-title">Buat Jadwal Pelajaran Baru</h3>
    </div>
    <div class="card-body">
        <?php if(session('error')): ?>
            <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
        <?php endif; ?>

        <form action="<?php echo e(route('jadwal.store')); ?>" method="POST" id="formJadwal">
            <?php echo csrf_field(); ?>

            <!-- Tabel Form Input -->
            <div class="table-responsive mb-4">
                <table class="table table-bordered table-form-input">
                    <thead class="thead-light">
                        <tr>
                            <th colspan="2" class="text-center">
                                <i class="fas fa-cog"></i> Pengaturan Jadwal Pelajaran
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td width="25%" class="font-weight-bold">
                                <i class="fas fa-th-large"></i> Pilih Layout
                            </td>
                            <td>
                                <?php
                                    $user = auth()->user();
                                    $isAdmin = $user->hasRole('Administrator');

                                    // Mapping unit_id ke layout
                                    $unitToLayout = [
                                        1 => 'layout_tk',
                                        2 => 'layout_sd',
                                        3 => 'layout_smp',
                                        4 => 'layout_sma'
                                    ];

                                    // Tentukan layout default berdasarkan unit_id user
                                    $defaultLayout = isset($unitToLayout[$user->unit_id]) ? $unitToLayout[$user->unit_id] : '';
                                ?>

                                <?php if($isAdmin): ?>
                                    
                                    <select name="layout_type" class="form-control" required>
                                        <option value="">Pilih Layout</option>
                                        <?php $__currentLoopData = $layouts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>" <?php echo e(old('layout_type') == $key ? 'selected' : ''); ?>>
                                                <?php echo e($value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                <?php else: ?>
                                    
                                    <select name="layout_type" class="form-control" required <?php echo e(!$isAdmin ? 'disabled' : ''); ?>>
                                        <?php $__currentLoopData = $layouts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $value): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($key); ?>"
                                                <?php echo e($key == $defaultLayout ? 'selected' : ''); ?>

                                                <?php echo e($key != $defaultLayout ? 'disabled' : ''); ?>>
                                                <?php echo e($value); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    
                                    <input type="hidden" name="layout_type" value="<?php echo e($defaultLayout); ?>">
                                <?php endif; ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-school"></i> Kelas
                            </td>
                            <td>
                                <select name="kelas_id" class="form-control <?php $__errorArgs = ['kelas_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                    <option value="">Pilih Kelas</option>
                                    <?php
                                        // Ambil tahun ajaran aktif
                                        $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();

                                        // Filter kelas berdasarkan tahun ajaran aktif
                                        $kelasFiltered = $kelas->filter(function($k) use ($tahunAjaranAktif) {
                                            return $k->tahun_ajaran == ($tahunAjaranAktif ? $tahunAjaranAktif->nama : null);
                                        });
                                    ?>
                                    <?php $__currentLoopData = $kelasFiltered; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $k): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($k->id); ?>" <?php echo e(old('kelas_id') == $k->id ? 'selected' : ''); ?>><?php echo e($k->nama); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['kelas_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-edit"></i> Nama Kelas (Teks)
                            </td>
                            <td>
                                <input type="text" name="nama_kelas_text" class="form-control <?php $__errorArgs = ['nama_kelas_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" value="<?php echo e(old('nama_kelas_text')); ?>" required placeholder="Contoh: VII A">
                                <?php $__errorArgs = ['nama_kelas_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-user-tie"></i> Wali Kelas
                            </td>
                            <td>
                                <select name="wali_kelas" class="form-control <?php $__errorArgs = ['wali_kelas'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                    <option value="">Pilih Wali Kelas</option>
                                    <?php
                                        // Ambil tahun ajaran aktif
                                        $tahunAjaranAktif = \App\Models\TahunAjaran::where('aktif', true)->first();

                                        // Ambil data guru dengan tugas tambahan Wali Kelas atau Guru Kelas
                                        // pada tahun ajaran yang aktif
                                        $waliKelas = DB::table('guru_tugastambahan')
                                            ->whereIn('tugas_tambahan', ['Wali Kelas', 'Guru Kelas'])
                                            ->where('tahun_ajaran_id', $tahunAjaranAktif ? $tahunAjaranAktif->id : null)
                                            ->orderBy('nama_guru')
                                            ->get();
                                    ?>
                                    <?php $__currentLoopData = $waliKelas; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $wali): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($wali->nama_guru); ?>" <?php echo e(old('wali_kelas') == $wali->nama_guru ? 'selected' : ''); ?>><?php echo e($wali->nama_guru); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['wali_kelas'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </td>
                        </tr>
                        <tr>
                            <td class="font-weight-bold">
                                <i class="fas fa-calendar-alt"></i> Tahun Ajaran
                            </td>
                            <td>
                                <select name="tahun_ajaran" class="form-control <?php $__errorArgs = ['tahun_ajaran'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" required>
                                    <option value="">Pilih Tahun Ajaran</option>
                                    <?php $__currentLoopData = $tahunAjaranList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tahun): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($tahun); ?>" <?php echo e(old('tahun_ajaran') == $tahun ? 'selected' : ''); ?>><?php echo e($tahun); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                                <?php $__errorArgs = ['tahun_ajaran'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div id="jadwalContainer" class="mt-4">
                <!-- Jadwal akan di-render di sini menggunakan JavaScript -->
            </div>

            <div class="text-center mt-4">
                <button type="submit" class="btn btn-primary btn-submit">
                    <i class="fas fa-save"></i> Simpan Jadwal Pelajaran
                </button>
            </div>
        </form>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('css'); ?>
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap4-theme@1.0.0/dist/select2-bootstrap4.min.css" rel="stylesheet" />
<style>
    /* Styling untuk tabel form input */
    .table-form-input {
        background-color: #f8f9fa;
    }

    .table-form-input thead th {
        background-color: #e9ecef;
        border-color: #dee2e6;
        font-weight: 600;
    }

    .table-form-input tbody td {
        vertical-align: middle;
        border-color: #dee2e6;
    }

    .table-form-input tbody td:first-child {
        background-color: #f1f3f4;
        font-weight: 500;
    }

    /* Styling untuk tabel jadwal */
    .jadwal-table {
        margin-top: 20px;
        font-size: 0.9rem;
    }

    .jadwal-table thead th {
        background-color: #007bff;
        color: white;
        text-align: center;
        font-weight: 600;
        border-color: #0056b3;
        padding: 12px 8px;
        vertical-align: middle;
    }

    .jadwal-table tbody td {
        vertical-align: middle;
        border-color: #dee2e6;
        padding: 8px;
    }

    /* Kolom jam (kolom pertama) */
    .jadwal-table .jam-cell {
        background-color: #e3f2fd;
        font-weight: 600;
        text-align: center;
        min-width: 120px;
        white-space: nowrap;
    }

    /* Kolom mata pelajaran */
    .jadwal-table .mapel-cell {
        background-color: #ffffff;
        min-width: 180px;
        position: relative;
    }

    /* Styling untuk select dalam tabel */
    .jadwal-table .form-control-sm {
        font-size: 0.8rem;
        padding: 4px 8px;
        height: auto;
        min-height: 32px;
    }

    /* Hover effect untuk sel */
    .jadwal-table .mapel-cell:hover {
        background-color: #f8f9fa;
    }

    /* Responsive untuk tabel jadwal */
    @media (max-width: 768px) {
        .jadwal-table {
            font-size: 0.8rem;
        }

        .jadwal-table .mapel-cell {
            min-width: 150px;
        }

        .jadwal-table .form-control-sm {
            font-size: 0.75rem;
            padding: 3px 6px;
        }
    }

    /* Icon styling */
    .table i {
        margin-right: 8px;
        color: #6c757d;
    }

    .thead-light th i {
        color: #495057;
    }

    /* Form control dalam tabel */
    .table .form-control {
        border-radius: 4px;
        border: 1px solid #ced4da;
    }

    .table .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    /* Button styling */
    .btn-submit {
        background: linear-gradient(45deg, #007bff, #0056b3);
        border: none;
        padding: 12px 30px;
        font-weight: 600;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
        transition: all 0.3s ease;
    }

    .btn-submit:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    }

    /* Styling untuk Select2 dalam tabel jadwal */
    .jadwal-table .select2-container {
        width: 100% !important;
    }

    .jadwal-table .select2-container .select2-selection--single {
        height: 32px !important;
        border: 1px solid #ced4da !important;
        border-radius: 4px !important;
    }

    .jadwal-table .select2-container .select2-selection--single .select2-selection__rendered {
        line-height: 30px !important;
        padding-left: 8px !important;
        font-size: 0.8rem !important;
    }

    .jadwal-table .select2-container .select2-selection--single .select2-selection__arrow {
        height: 30px !important;
        right: 5px !important;
    }

    /* Zebra striping untuk baris */
    .jadwal-table tbody tr:nth-child(even) .mapel-cell {
        background-color: #f8f9fa;
    }

    .jadwal-table tbody tr:nth-child(odd) .mapel-cell {
        background-color: #ffffff;
    }

    /* Highlight untuk jam istirahat */
    .jadwal-table .jam-istirahat {
        background-color: #fff3cd !important;
        color: #856404;
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('js'); ?>
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    const layouts = {
        layout_tk: { // TK - 10 Jam Pelajaran
            jamPelajaran: 10,
            waktu: [
                { mulai: '07:30', selesai: '08:00' },
                { mulai: '08:00', selesai: '08:30' },
                { mulai: '08:30', selesai: '09:00' },
                { mulai: '09:00', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat
                { mulai: '09:45', selesai: '10:15' },
                { mulai: '10:15', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:15' },
                { mulai: '11:15', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:15' }
            ]
        },
        layout_sd: { // SD - 14 Jam Pelajaran
            jamPelajaran: 14,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:05' },
                { mulai: '08:05', selesai: '08:40' },
                { mulai: '08:40', selesai: '09:15' },
                { mulai: '09:15', selesai: '09:45' },
                { mulai: '09:45', selesai: '10:00' }, // Istirahat 1
                { mulai: '10:00', selesai: '10:30' },
                { mulai: '10:30', selesai: '11:00' },
                { mulai: '11:00', selesai: '11:30' },
                { mulai: '11:30', selesai: '12:00' },
                { mulai: '12:00', selesai: '12:25' }, // Istirahat 2
                { mulai: '12:25', selesai: '12:55' },
                { mulai: '12:55', selesai: '13:25' },
                { mulai: '13:25', selesai: '13:55' }
            ]
        },
        layout_smp: { // SMP - 16 Jam Pelajaran
            jamPelajaran: 16,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:45' },
                { mulai: '08:45', selesai: '09:20' },
                { mulai: '09:20', selesai: '09:35' }, // Istirahat 1
                { mulai: '09:35', selesai: '10:10' },
                { mulai: '10:10', selesai: '10:45' },
                { mulai: '10:45', selesai: '11:20' },
                { mulai: '11:20', selesai: '11:55' },
                { mulai: '11:55', selesai: '12:45' }, // Istirahat 2
                { mulai: '12:45', selesai: '13:20' },
                { mulai: '13:20', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:30' },
                { mulai: '14:30', selesai: '14:45' }, // Istirahat 3
                { mulai: '14:45', selesai: '15:20' },
                { mulai: '15:20', selesai: '15:55' }
            ]
        },
        layout_sma: { // SMA - 18 Jam Pelajaran
            jamPelajaran: 18,
            waktu: [
                { mulai: '07:15', selesai: '07:30' },
                { mulai: '07:30', selesai: '08:10' },
                { mulai: '08:10', selesai: '08:50' },
                { mulai: '08:50', selesai: '09:30' },
                { mulai: '09:30', selesai: '09:45' }, // Istirahat 1
                { mulai: '09:45', selesai: '10:25' },
                { mulai: '10:25', selesai: '11:05' },
                { mulai: '11:05', selesai: '11:45' },
                { mulai: '11:45', selesai: '12:25' },
                { mulai: '12:25', selesai: '13:15' }, // Istirahat 2
                { mulai: '13:15', selesai: '13:55' },
                { mulai: '13:55', selesai: '14:35' },
                { mulai: '14:35', selesai: '15:15' },
                { mulai: '15:15', selesai: '15:30' }, // Istirahat 3
                { mulai: '15:30', selesai: '16:10' },
                { mulai: '16:10', selesai: '16:50' },
                { mulai: '16:50', selesai: '17:30' },
                { mulai: '17:30', selesai: '18:10' }
            ]
        }
    };

    // Variabel untuk menyimpan layout terakhir yang dipilih
    let lastSelectedLayout = '';
    
    // Variabel untuk menyimpan seleksi saat ini
    let savedSelections = {};
    
    // Fungsi untuk menyimpan seleksi saat ini
    function saveCurrentSelections() {
        // Simpan seleksi jadwal
        $('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const value = $(this).val();
            if (value) {
                savedSelections[name] = value;
            }
        });
        
        // Simpan juga nilai form utama
        savedSelections['kelas_id'] = $('select[name="kelas_id"]').val();
        savedSelections['nama_kelas_text'] = $('input[name="nama_kelas_text"]').val();
        savedSelections['wali_kelas'] = $('select[name="wali_kelas"]').val();
        savedSelections['tahun_ajaran'] = $('select[name="tahun_ajaran"]').val();
    }
    
    // Fungsi untuk mengembalikan seleksi yang disimpan
    function restoreSelections() {
        // Kembalikan seleksi jadwal
        for (const name in savedSelections) {
            if (name.includes('[mapel_id]')) {
                $(`select[name="${name}"]`).val(savedSelections[name]);
            }
        }
        
        // Kembalikan nilai form utama jika ada
        if (savedSelections['kelas_id']) $('select[name="kelas_id"]').val(savedSelections['kelas_id']);
        if (savedSelections['nama_kelas_text']) $('input[name="nama_kelas_text"]').val(savedSelections['nama_kelas_text']);
        if (savedSelections['wali_kelas']) $('select[name="wali_kelas"]').val(savedSelections['wali_kelas']);
        if (savedSelections['tahun_ajaran']) $('select[name="tahun_ajaran"]').val(savedSelections['tahun_ajaran']);
    }
    
    // Fungsi untuk render jadwal berdasarkan layout yang dipilih
    function renderJadwalTable(layoutType) {
        if (!layoutType) return;

        // Simpan seleksi saat ini sebelum render ulang
        saveCurrentSelections();

        const layout = layouts[layoutType];
        if (!layout) return;

        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];

        let html = `
            <div class="mt-4">
                <h4 class="text-primary mb-3">
                    <i class="fas fa-table"></i> Jadwal Pelajaran
                </h4>
                <div class="table-responsive">
                    <table class="table table-bordered jadwal-table">
                        <thead>
                            <tr>
                                <th width="15%" class="text-center">Jam</th>`;

        // Header hari
        hari.forEach(h => {
            html += `<th class="text-center">${h}</th>`;
        });

        html += `
                            </tr>
                        </thead>
                        <tbody>`;

        // Baris untuk setiap waktu
        layout.waktu.forEach((w, wIndex) => {
            html += `
                <tr>
                    <td class="jam-cell font-weight-bold text-center">${w.mulai}-${w.selesai}</td>`;

            // Kolom untuk setiap hari
            hari.forEach((h, hIndex) => {
                const selectName = `jadwal[${hIndex}_${wIndex}][mapel_id]`;

                html += `
                    <td class="mapel-cell">
                        <select name="${selectName}" class="form-control form-control-sm" data-hari="${h}" data-waktu-mulai="${w.mulai}" data-waktu-selesai="${w.selesai}">
                            <option value="">-- Kosong --</option>
                            <optgroup label="Mata Pelajaran">
                                <?php $__currentLoopData = $mataPelajaran; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $mp): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($mp->id); ?>"><?php echo e($mp->nama_mapel); ?> (<?php echo e($mp->pengajar->name); ?>)</option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                            <optgroup label="Kegiatan Khusus">
                                <?php $__currentLoopData = $specialEvents; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $event): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($event['id']); ?>"><?php echo e($event['nama']); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </optgroup>
                        </select>
                        <input type="hidden" name="jadwal[${hIndex}_${wIndex}][hari]" value="${h}">
                        <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_mulai]" value="${w.mulai}">
                        <input type="hidden" name="jadwal[${hIndex}_${wIndex}][waktu_selesai]" value="${w.selesai}">
                    </td>`;
            });

            html += `</tr>`;
        });

        html += `
                        </tbody>
                    </table>
                </div>
            </div>
        `;

        $('#jadwalContainer').html(html);
        
        // Kembalikan seleksi yang disimpan
        restoreSelections();
        
        // Setup handler untuk perubahan mapel
        setupMapelChangeHandler();
        
        // Initialize Select2 for mata pelajaran dropdowns
        $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
            theme: 'bootstrap4',
            placeholder: "Pilih Mata Pelajaran",
            allowClear: true,
            width: '100%',
            dropdownParent: $('#jadwalContainer'),
            language: {
                searching: function() {
                    return "Ketik nama guru atau mata pelajaran...";
                }
            }
        });
    }
    
    // Handler untuk perubahan layout
    $('select[name="layout_type"]').change(function() {
        const layoutType = $(this).val();
        renderJadwalTable(layoutType);
        lastSelectedLayout = layoutType;
    });
    
    // Untuk user non-admin, ambil nilai default layout dari hidden input
    const defaultLayout = $('input[name="layout_type"]').val() || $('select[name="layout_type"]').val();
    if (defaultLayout) {
        // Render jadwal dengan layout default
        renderJadwalTable(defaultLayout);
        lastSelectedLayout = defaultLayout;
    } else {
        // Trigger change event untuk admin yang harus memilih layout
        $('select[name="layout_type"]').trigger('change');
    }
    
    // Tambahkan event handler untuk menyimpan nilai form utama saat berubah
    $('select[name="kelas_id"], input[name="nama_kelas_text"], select[name="wali_kelas"], select[name="tahun_ajaran"]').change(function() {
        saveCurrentSelections();
    });
    
    // Tambahkan validasi form
    $('#formJadwal').submit(function(e) {
        e.preventDefault(); // Prevent default submission

        // Validasi yang sudah ada
        const jadwalPerHari = {};
        const hari = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat'];
        hari.forEach(h => jadwalPerHari[h] = 0);

        let error = false;
        let bentrokFound = false;

        // Cek jumlah mapel per hari
        $(this).find('select[name$="[mapel_id]"]').each(function() {
            const name = $(this).attr('name');
            const hari = $(`input[name="${name.replace('[mapel_id]', '[hari]')}"]`).val();
            const value = $(this).val();
            if (value && !value.includes('istirahat')) {
                jadwalPerHari[hari]++;
            }

            // Cek bentrok untuk setiap mapel
            if (checkBentrok($(this))) {
                bentrokFound = true;
            }
        });

        // Validasi minimal 1 mapel per hari
        hari.forEach(h => {
            if (jadwalPerHari[h] < 1) {
                Swal.fire({
                    title: 'Error!',
                    text: `Hari ${h} harus memiliki minimal 1 mata pelajaran`,
                    icon: 'error',
                    confirmButtonText: 'OK'
                });
                error = true;
            }
        });

        if (!error && !bentrokFound) {
            // Jika tidak ada error dan bentrok, submit form
            this.submit();
        }
    });
});

function checkBentrok(changedSelect) {
    const selectedMapelId = changedSelect.val();
    if (!selectedMapelId || selectedMapelId.includes('istirahat') || 
        selectedMapelId.includes('upacara') || selectedMapelId.includes('kebaktian')) {
        return false;
    }

    const hari = changedSelect.data('hari');
    const waktuMulai = changedSelect.data('waktu-mulai');
    const waktuSelesai = changedSelect.data('waktu-selesai');
    let bentrok = false;

    $('select[name$="[mapel_id]"]').each(function() {
        const $otherSelect = $(this);
        if ($otherSelect.attr('name') !== changedSelect.attr('name') && 
            $otherSelect.val() === selectedMapelId &&
            $otherSelect.data('hari') === hari) {
            
            const otherMulai = $otherSelect.data('waktu-mulai');
            const otherSelesai = $otherSelect.data('waktu-selesai');

            if (isTimeOverlap(waktuMulai, waktuSelesai, otherMulai, otherSelesai)) {
                const message = `Peringatan: Terdapat bentrok jadwal guru pada hari ${hari} antara jam ${waktuMulai}-${waktuSelesai} dengan jam ${otherMulai}-${otherSelesai}`;
                Swal.fire({
                    title: 'Bentrok Terdeteksi!',
                    text: message,
                    icon: 'warning',
                    confirmButtonText: 'OK'
                });
                bentrok = true;
            }
        }
    });
    return bentrok;
}

function isTimeOverlap(start1, end1, start2, end2) {
    return (start1 < end2 && end1 > start2);
}

// Fungsi untuk setup event handler pada select mapel
function setupMapelChangeHandler() {
    $('select[name$="[mapel_id]"]').change(function() {
        checkBentrok($(this));
    });
}

// Jika ada old input dari controller, isi kembali form
<?php if(old('jadwal')): ?>
    $(document).ready(function() {
        // Tunggu sampai jadwal container dirender
        setTimeout(function() {
            <?php $__currentLoopData = old('jadwal'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php if(isset($detail['mapel_id']) && $detail['mapel_id']): ?>
                    $('select[name="jadwal[<?php echo e($key); ?>][mapel_id]"]').val('<?php echo e($detail['mapel_id']); ?>');
                <?php endif; ?>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        }, 500);
    });
<?php endif; ?>

    // Initialize Select2 for mata pelajaran dropdowns
    $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
        theme: 'bootstrap4',
        placeholder: "Pilih Mata Pelajaran",
        allowClear: true,
        width: '100%',
        dropdownParent: $('#jadwalContainer'),
        language: {
            searching: function() {
                return "Ketik nama guru atau mata pelajaran...";
            }
        }
    });
    
    // Re-initialize Select2 when jadwal is rendered
    $('select[name="layout_type"]').on('change', function() {
        // Wait for the jadwal to be rendered
        setTimeout(function() {
            $('select[name^="jadwal"][name$="[mapel_id]"]').select2({
                theme: 'bootstrap4',
                placeholder: "Pilih Mata Pelajaran",
                allowClear: true,
                width: '100%',
                dropdownParent: $('#jadwalContainer'),
                language: {
                    searching: function() {
                        return "Ketik nama guru atau mata pelajaran...";
                    }
                }
            });
        }, 500);
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\simaspeloporh2_Jadwal Ok2\resources\views/jadwal/create.blade.php ENDPATH**/ ?>