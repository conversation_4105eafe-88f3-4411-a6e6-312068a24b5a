<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Title
    |--------------------------------------------------------------------------
    |
    | Here you can change the default title of your admin panel.
    |
    | For detailed instructions you can look the title section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'title' => 'Adminplp',
    'title_prefix' => '',
    'title_postfix' => '',

    /*
    |--------------------------------------------------------------------------
    | Favicon
    |--------------------------------------------------------------------------
    |
    | Here you can activate the favicon.
    |
    | For detailed instructions you can look the favicon section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_ico_only' => false,
    'use_full_favicon' => false,

    /*
    |--------------------------------------------------------------------------
    | Google Fonts
    |--------------------------------------------------------------------------
    |
    | Here you can allow or not the use of external google fonts. Disabling the
    | google fonts may be useful if your admin panel internet access is
    | restricted somehow.
    |
    | For detailed instructions you can look the google fonts section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'google_fonts' => [
        'allowed' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Admin Panel Logo
    |--------------------------------------------------------------------------
    |
    | Here you can change the logo of your admin panel.
    |
    | For detailed instructions you can look the logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'logo' => '<b>SIMAS Pelopor</b>',
    'logo_img' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
    'logo_img_class' => 'brand-image img-circle elevation-3',
    'logo_img_xl' => null,
    'logo_img_xl_class' => 'brand-image-xs',
    'logo_img_alt' => 'Admin Logo',

    /*
    |--------------------------------------------------------------------------
    | Authentication Logo
    |--------------------------------------------------------------------------
    |
    | Here you can setup an alternative logo to use on your login and register
    | screens. When disabled, the admin panel logo will be used instead.
    |
    | For detailed instructions you can look the auth logo section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'auth_logo' => [
        'enabled' => false,
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'Auth Logo',
            'class' => '',
            'width' => 50,
            'height' => 50,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Preloader Animation
    |--------------------------------------------------------------------------
    |
    | Here you can change the preloader animation configuration. Currently, two
    | modes are supported: 'fullscreen' for a fullscreen preloader animation
    | and 'cwrapper' to attach the preloader animation into the content-wrapper
    | element and avoid overlapping it with the sidebars and the top navbar.
    |
    | For detailed instructions you can look the preloader section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'preloader' => [
        'enabled' => false,
        'mode' => 'fullscreen',
        'img' => [
            'path' => 'vendor/adminlte/dist/img/AdminLTELogo.png',
            'alt' => 'AdminLTE Preloader Image',
            'effect' => 'animation__shake',
            'width' => 60,
            'height' => 60,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | User Menu
    |--------------------------------------------------------------------------
    |
    | Here you can activate and change the user menu.
    |
    | For detailed instructions you can look the user menu section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'usermenu_enabled' => true,
    'usermenu_header' => false,
    'usermenu_header_class' => 'bg-primary',
    'usermenu_image' => false,
    'usermenu_desc' => false,
    'usermenu_profile_url' => false,

    /*
    |--------------------------------------------------------------------------
    | Layout
    |--------------------------------------------------------------------------
    |
    | Here we change the layout of your admin panel.
    |
    | For detailed instructions you can look the layout section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'layout_topnav' => null,
    'layout_boxed' => null,
    'layout_fixed_sidebar' => true,
    'layout_fixed_navbar' => true,
    'layout_fixed_footer' => null,
    'layout_dark_mode' => null,

    /*
    |--------------------------------------------------------------------------
    | Authentication Views Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the authentication views.
    |
    | For detailed instructions you can look the auth classes section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_auth_card' => 'card-outline card-primary',
    'classes_auth_header' => '',
    'classes_auth_body' => '',
    'classes_auth_footer' => '',
    'classes_auth_icon' => '',
    'classes_auth_btn' => 'btn-flat btn-primary',

    /*
    |--------------------------------------------------------------------------
    | Admin Panel Classes
    |--------------------------------------------------------------------------
    |
    | Here you can change the look and behavior of the admin panel.
    |
    | For detailed instructions you can look the admin panel classes here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'classes_body' => '',
    'classes_brand' => '',
    'classes_brand_text' => 'd-block text-sm',
    'classes_content_wrapper' => '',
    'classes_content_header' => '',
    'classes_content' => '',
    'classes_sidebar' => 'sidebar-dark-primary elevation-4',
    'classes_sidebar_nav' => 'nav-child-indent nav-compact submenu-light',
    'classes_topnav' => 'navbar-white navbar-light',
    'classes_topnav_nav' => 'navbar-expand',
    'classes_topnav_container' => 'container',

    /*
    |--------------------------------------------------------------------------
    | Sidebar
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar of the admin panel.
    |
    | For detailed instructions you can look the sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'sidebar_mini' => 'lg',
    'sidebar_collapse' => false,
    'sidebar_collapse_auto_size' => false,
    'sidebar_collapse_remember' => false,
    'sidebar_collapse_remember_no_transition' => true,
    'sidebar_scrollbar_theme' => 'os-theme-light',
    'sidebar_scrollbar_auto_hide' => 'l',
    'sidebar_nav_accordion' => true,
    'sidebar_nav_animation_speed' => 300,

    /*
    |--------------------------------------------------------------------------
    | Control Sidebar (Right Sidebar)
    |--------------------------------------------------------------------------
    |
    | Here we can modify the right sidebar aka control sidebar of the admin panel.
    |
    | For detailed instructions you can look the right sidebar section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Layout-and-Styling-Configuration
    |
    */

    'right_sidebar' => false,
    'right_sidebar_icon' => 'fas fa-cogs',
    'right_sidebar_theme' => 'dark',
    'right_sidebar_slide' => true,
    'right_sidebar_push' => true,
    'right_sidebar_scrollbar_theme' => 'os-theme-light',
    'right_sidebar_scrollbar_auto_hide' => 'l',

    /*
    |--------------------------------------------------------------------------
    | URLs
    |--------------------------------------------------------------------------
    |
    | Here we can modify the url settings of the admin panel.
    |
    | For detailed instructions you can look the urls section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Basic-Configuration
    |
    */

    'use_route_url' => false,
    'dashboard_url' => 'dashboard', // Ubah dari 'home' menjadi 'dashboard'
    'logout_url' => 'logout',
    'login_url' => 'login',
    'register_url' => 'register',
    'password_reset_url' => 'password/reset',
    'password_email_url' => 'password/email',
    'profile_url' => false,
    'disable_darkmode_routes' => false,

    /*
    |--------------------------------------------------------------------------
    | Laravel Asset Bundling
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Laravel Asset Bundling option for the admin panel.
    | Currently, the next modes are supported: 'mix', 'vite' and 'vite_js_only'.
    | When using 'vite_js_only', it's expected that your CSS is imported using
    | JavaScript. Typically, in your application's 'resources/js/app.js' file.
    | If you are not using any of these, leave it as 'false'.
    |
    | For detailed instructions you can look the asset bundling section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'laravel_asset_bundling' => false,
    'laravel_css_path' => 'css/app.css',
    'laravel_js_path' => 'js/app.js',

    /*
    |--------------------------------------------------------------------------
    | Menu Items
    |--------------------------------------------------------------------------
    |
    | Here we can modify the sidebar/top navigation of the admin panel.
    |
    | For detailed instructions you can look here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'menu' => [
        [
            'text' => 'Dashboard',
            'url'  => 'dashboard',
            'icon' => 'fas fa-fw fa-tachometer-alt',
        ],
        [
            'text' => 'Notifikasi',
            'url'  => 'notifikasi',
            'icon' => 'fas fa-bell',
        ],
        [
            'text' => 'GTK',
            'url'  => 'gtk',
            'icon' => 'fas fa-fw fa-users',
            'can'  => 'view-gtk',
            'submenu' => [
                [
                    'text' => 'Guru',
                    'url'  => 'gtk/guru',
                    'icon' => 'fas fa-fw fa-chalkboard-teacher',
                ],
                [
                    'text' => 'Tendik',
                    'url'  => 'gtk/tendik',
                    'icon' => 'fas fa-fw fa-user-cog',
                ],
                [
                    'text' => 'GTK Non-Aktif',
                    'url'  => 'gtk-nonaktif',
                    'icon' => 'fas fa-fw fa-user-slash',
                ],
            ],
        ],
        [
            'text' => 'Peserta Didik',
            'url'  => 'peserta-didik',
            'icon' => 'fas fa-fw fa-user-graduate',
            'can'  => 'view-peserta-didik',
            'submenu' => [
                [
                    'text' => 'Peserta Didik Aktif',
                    'url'  => 'peserta-didik/aktif',
                    'icon' => 'fas fa-fw fa-user-check',
                    'can'  => 'view-peserta-didik-aktif',  // Tambahkan permission ini
                ],
                [
                    'text' => 'PD Alumni',
                    'url'  => 'peserta-didik/alumni',
                    'icon' => 'fas fa-fw fa-user-graduate',
                    'can'  => 'manage-peserta-didik',  // Permission untuk admin/staff
                ],
                [
                    'text' => 'PD Keluar',
                    'url'  => 'peserta-didik/mutasi-keluar',
                    'icon' => 'fas fa-fw fa-user-times',
                    'can'  => 'manage-peserta-didik',  // Permission untuk admin/staff
                ],
            ],
        ],
        [
            'text'    => 'Jurnal Kegiatan',
            'icon'    => 'fas fa-fw fa-book',
            'can'     => 'view-jurnal',
            'submenu' => [
                [
                    'text' => 'Daftar Jurnal',
                    'url'  => 'jurnal/list',
                    'icon' => 'fas fa-fw fa-list',
                ],
                [
                    'text' => 'Jurnal Disetujui',
                    'url'  => 'jurnal/approved',
                    'icon' => 'fas fa-fw fa-check',
                ],
                [
                    'text' => 'Rekap Jurnal',
                    'url'  => 'jurnal/recap',
                    'icon' => 'fas fa-fw fa-chart-bar',
                ],
                [
                    'text' => 'Approval Jurnal',
                    'url'  => 'jurnal/approval',
                    'icon' => 'fas fa-fw fa-check-circle',
                    'can'  => 'approve-jurnal',
                ],
            ],
        ],
        [
            'text'    => 'Jadwal',
            'icon'    => 'fas fa-fw fa-calendar',
            'can'     => ['view-jadwal', 'view-mengajar'],
            'submenu' => [
                [
                    'text' => 'Daftar Jadwal',
                    'url'  => 'jadwal',
                    'icon' => 'fas fa-fw fa-list',
                    'can'  => 'view-jadwal'
                ],
                [
                    'text' => 'Tampilan Tabel',
                    'url'  => 'jadwal/jadlaykot',
                    'icon' => 'fas fa-fw fa-table',
                    'can'  => 'view-jadwal'
                ],
                [
                    'text' => 'Jadwal Mengajar',
                    'url'  => 'jadwal/mengajar',
                    'icon' => 'fas fa-fw fa-chalkboard-teacher',
                    'can'  => 'view-mengajar'
                ],
            ],
        ],
        [
            'text' => 'E-Learning',
            'url'  => 'https://elearning.sekolahpeloporduri.sch.id',
            'icon' => 'fas fa-fw fa-laptop',
            'target' => '_blank',
        ],
        [
            'text'    => 'ADM',
            'icon'    => 'fas fa-fw fa-folder',
            'can'     => ['view-adm', 'manage-adm', 'view-adm-kepsek', 'view-adm-ktsp'], // Tambahkan permission yang dimiliki Pengawas
            'submenu' => [
                [
                    'text' => 'ADM Guru',
                    'url'  => 'adm/guru',
                    'icon' => 'fas fa-fw fa-chalkboard-teacher',
                    'can'  => 'manage-adm-guru', // Gunakan permission yang konsisten
                ],
                [
                    'text' => 'ADM Waka',
                    'url'  => 'adm/waka',
                    'icon' => 'fas fa-fw fa-user-tie',
                    'can'  => 'manage-adm-waka',
                ],
                [
                    'text' => 'ADM Kepala Sekolah',
                    'url'  => 'adm/kepsek',
                    'icon' => 'fas fa-fw fa-user-shield',
                    'can'  => ['view-adm-kepsek', 'manage-adm-kepsek'], // Gunakan permission yang sesuai
                ],
                [
                    'text' => 'KTSP',
                    'url'  => 'adm/ktsp',
                    'icon' => 'fas fa-fw fa-book',
                    'can'  => ['manage-adm', 'view-adm-ktsp'], // Tambahkan view-adm-ktsp
                ],
                /*
                [
                    'text' => 'SKP',
                    'url'  => 'adm/skp',
                    'icon' => 'fas fa-fw fa-file-alt',
                    'can'  => 'manage-adm',
                ], */
            ],
        ],
        [
            'text' => 'Rombongan Belajar',
            'icon' => 'fas fa-fw fa-users',
            'can'  => 'view-rombel', // Tambahkan permission untuk melihat menu rombel
            'submenu' => [
                [
                    'text' => 'Reguler',
                    'url'  => 'rombel/reguler',
                    'submenu' => [
                        [
                            'text' => 'Daftar Kelas',
                            'url'  => 'rombel/reguler/daftar',
                            'icon' => 'fas fa-fw fa-list',
                            'can'  => 'view-daftar-kelas', // Permission khusus untuk melihat daftar kelas
                        ],
                        [
                            'text' => 'Penempatan Siswa',
                            'url'  => 'rombel/reguler/penempatan',
                            'icon' => 'fas fa-fw fa-user-plus',
                            'can'  => 'assign-siswa-rombel', // Permission untuk menempatkan siswa
                        ],
                        [
                            'text' => 'Pindah Kelas',
                            'url'  => 'rombel/reguler/pindah-kelas',
                            'icon' => 'fas fa-fw fa-exchange-alt',
                            'can'  => ['assign-siswa-rombel', 'remove-siswa-rombel'], // Permission untuk memindahkan siswa
                        ],
                        [
                            'text' => 'Kenaikan Kelas',
                            'url'  => 'rombel/reguler/kenaikan',
                            'icon' => 'fas fa-fw fa-arrow-up',
                            'can'  => 'manage-rombel', // Permission untuk mengelola kenaikan kelas
                        ],
                        [
                            'text' => 'Kelulusan',
                            'url'  => 'rombel/reguler/kelulusan',
                            'icon' => 'fas fa-fw fa-graduation-cap',
                            'can'  => 'manage-rombel', // Permission untuk mengelola kelulusan
                        ],
                    ],
                ],
                [
                    'text' => 'Eksul',
                    'url'  => 'rombel/eksul',
                    'can'  => 'view-rombel', // Permission untuk melihat ekstrakurikuler
                ],
            ],
        ],

        [
            'text'    => 'Absensi',
            'icon'    => 'fas fa-fw fa-clipboard-check',
            'can'     => 'view-absensi', // Tambahkan permission untuk melihat menu absensi
            'submenu' => [
                [
                    'text' => 'Absen Harian',
                    'url'  => 'absensi/harian',
                    'icon' => 'fas fa-fw fa-calendar-day',
                    'can'  => ['create-absensi', 'edit-absensi'], // Permission untuk mengelola absen harian
                ],
                [
                    'text' => 'Rekap Absen',
                    'url'  => 'absensi/rekap',
                    'icon' => 'fas fa-fw fa-list-alt',
                    'can'  => 'view-rekap-absensi', // Permission untuk melihat rekap absensi
                ],
                [
                    'text' => 'Rekap Admin',
                    'url'  => 'absensi/rekap-admin',
                    'icon' => 'fas fa-fw fa-chart-bar',
                    'can'  => 'view-rekap-admin', // Permission untuk melihat rekap admin
                ],
            ],
        ],
        [
            'text'    => 'Sarana dan Prasarana',
            'icon'    => 'fas fa-fw fa-building',
            'can'     => 'view-sarpras',
            'submenu' => [
                [
                    'text' => 'Data Sarpras',
                    'url'  => 'sarana',
                    'icon' => 'fas fa-fw fa-database',
                    'can'  => 'view-sarpras',
                ],
                [
                    'text' => 'Laporan Kerusakan',
                    'url'  => 'laporan-kerusakan',
                    'icon' => 'fas fa-fw fa-exclamation-triangle',
                    'can'  => 'view-laporan-kerusakan',
                ],
                [
                    'text' => 'Kelola Laporan Kerusakan',
                    'url'  => 'laporan-kerusakan',
                    'icon' => 'fas fa-fw fa-tools',
                    'can'  => 'manage-laporan-kerusakan',
                ],
            ],
        ],
        [
           //'text' => 'Manajemen Pengguna',
           // 'url'  => 'users',
           // 'icon' => 'fas fa-fw fa-users-cog',
           // 'can'  => 'manage-users',
        ],
        [
            'text' => 'Tahun Ajaran',
            'url'  => 'tahunajaran',
            'icon' => 'fas fa-fw fa-calendar-alt',
            'can'  => 'view-tahunajaran',
        ],
        [
            'text' => 'Cek SPP',
            'url'  => 'spp',
            'icon' => 'fas fa-fw fa-money-bill',
            'can'  => 'view-spp',
        ],
        [
            'text'    => 'Website',
            'icon'    => 'fas fa-fw fa-globe',
            'can'     => 'view-website', // Tambahkan permission untuk melihat menu website
            'submenu' => [
                [
                    'text' => 'Artikel',
                    'url'  => 'website/artikel',
                    'can'  => 'view-website-artikel', // Permission untuk melihat artikel
                ],
                [
                    'text' => 'Event',
                    'url'  => '/admin/website/event',
                    'can'  => 'view-website-event', // Permission untuk melihat event
                ],
                [
                    'text' => 'Prestasi',
                    'url'  => 'admin/website/prestasi',
                    'can'  => 'view-website-prestasi', // Permission untuk melihat prestasi
                ],
                [
                    'text' => 'Fasilitas',
                    'url'  => 'admin/website/facility',
                    'can'  => 'view-website-fasilitas', // Permission untuk melihat fasilitas
                ],
                [
                    'text' => 'Extrakulikuler',
                    'url'  => 'admin/website/ekstrakurikuler',
                    'can'  => 'view-website-ekstrakurikuler', // Permission untuk melihat ekstrakurikuler
                ],
                [
                    'text' => 'Slide',
                    'url'  => 'website/slide',
                    'can'  => 'view-website-slide', // Permission untuk melihat slide
                ],
                [
                    'text' => 'Halaman',
                    'url'  => 'admin/website/halaman',
                    'icon' => 'fas fa-fw fa-file-alt',
                    'can'  => 'view-website-halaman', // Permission untuk melihat halaman
                ],
            ],
        ],
        [
            'text'    => 'Pengaturan',
            'icon'    => 'fas fa-fw fa-cogs',
            'can'     => 'view-pengaturan', // Tambahkan permission untuk melihat menu pengaturan
            'submenu' => [
                [
                    'text' => 'Unit',
                    'url'  => 'pengaturan/unit',
                    'can'  => 'view-unit', // Permission untuk melihat unit
                ],
                [
                    'text' => 'Jenjang',
                    'url'  => 'pengaturan/jenjang',
                    'can'  => 'view-jenjang', // Permission untuk melihat jenjang
                    'active' => ['pengaturan/jenjang', 'pengaturan/jenjang/*']
                ],
                [
                    'text' => 'Gedung',
                    'url'  => 'pengaturan/gedung',
                    'can'  => 'view-gedung', // Permission untuk melihat gedung
                ],
                [
                    'text' => 'Kelas',
                    'url'  => 'pengaturan/kelas',
                    'can'  => 'view-kelas', // Permission untuk melihat kelas
                ],
                [
                    'text' => 'Mata Pelajaran',
                    'url'  => 'pengaturan/mapel',
                    'can'  => 'view-mapel', // Permission untuk melihat mata pelajaran
                ],
            ],
        ],
        [
            'text'    => 'Bimbingan Konseling',
            'icon'    => 'fas fa-fw fa-heart',
            'submenu' => [
                [
                    'text' => 'Pelanggaran',
                    'url'  => 'bk/pelanggaran',
                ],
                [
                    'text' => 'Sanksi',
                    'url'  => 'bk/sanksi',
                ],
                [
                    'text' => 'Data Pelanggaran',
                    'url'  => 'bk/data-pelanggaran',
                ],
            ],
        ],
        // Menu Penilaian
        [
            'text'    => 'Penilaian',
            'icon'    => 'fas fa-chart-bar',
            'submenu' => [
                [
                    'text' => 'Dashboard Nilai',
                    'url'  => 'nilai',
                    'icon' => 'fas fa-tachometer-alt',
                    'active' => ['nilai'],
                ],
                [
                    'text' => 'Kompetensi Dasar',
                    'url'  => 'penilaian/kompetensi',
                    'icon' => 'fas fa-book',
                    'active' => ['penilaian/kompetensi*'],
                ],
                [
                    'text' => 'Tujuan Pembelajaran',
                    'url'  => 'penilaian/tujuan-pembelajaran',
                    'icon' => 'fas fa-bullseye',
                    'active' => ['penilaian/tujuan-pembelajaran*'],
                ],
                [
                    'text' => 'Penilaian Formatif',
                    'url'  => 'penilaian/formatif',
                    'icon' => 'fas fa-tasks',
                    'active' => ['penilaian/formatif*'],
                ],
                [
                    'text' => 'Penilaian Sumatif',
                    'url'  => 'penilaian/sumatif',
                    'icon' => 'fas fa-chart-line',
                    'active' => ['penilaian/sumatif*'],
                ],
                [
                    'text' => 'Penilaian Proyek',
                    'url'  => 'penilaian/proyek',
                    'icon' => 'fas fa-project-diagram',
                    'active' => ['penilaian/proyek*'],
                ],
                [
                    'text' => 'Laporan Nilai',
                    'icon' => 'fas fa-file-alt',
                    'submenu' => [
                        [
                            'text' => 'Laporan Per Kelas',
                            'url'  => 'nilai/laporan-kelas',
                            'icon' => 'fas fa-users',
                        ],
                        [
                            'text' => 'Cari Siswa',
                            'url'  => 'nilai/cari-siswa',
                            'icon' => 'fas fa-search',
                        ],
                    ],
                ],
                [
                    'text' => 'Cakapan Pembelajaran',
                    'url'  => 'penilaian/cakapan-pembelajaran',
                    'icon' => 'fas fa-comments',
                    'active' => ['penilaian/cakapan-pembelajaran*'],
                ],
            ],
        ],
        // Menu Pengaturan
        [
            'text'    => 'Manajemen Akses',
            'icon'    => 'fas fa-fw fa-user-shield',
            'can'     => ['manage-users', 'view-users', 'view-roles', 'view-permissions'], // Izinkan akses jika memiliki salah satu permission
            'submenu' => [
                [
                    'text' => 'Users',
                    'url'  => 'admin/users',
                    'icon' => 'fas fa-fw fa-users',
                    'can'  => ['manage-users', 'view-users'], // Izinkan akses jika memiliki salah satu permission
                ],
                [
                    'text' => 'Roles',
                    'url'  => 'admin/roles',
                    'icon' => 'fas fa-fw fa-user-tag',
                    'can'  => ['manage-users', 'view-roles'], // Izinkan akses jika memiliki salah satu permission
                ],
                [
                    'text' => 'Permissions',
                    'url'  => 'admin/permissions',
                    'icon' => 'fas fa-fw fa-key',
                    'can'  => ['manage-permissions', 'view-permissions'], // Izinkan akses jika memiliki salah satu permission
                ],
            ],
        ],
        [
            'text'    => 'Laporan Kerja',
            'icon'    => 'fas fa-fw fa-tasks',
            'can'     => ['manage-laporan-kerja', 'view-laporan-kerja'],
            'submenu' => [
                [
                    'text' => 'Daftar Laporan',
                    'url'  => 'laporan-kerja',
                    'icon' => 'fas fa-fw fa-list',
                ],
                [
                    'text' => 'Tambah Laporan',
                    'url'  => 'laporan-kerja/create',
                    'icon' => 'fas fa-fw fa-plus',
                    'can'  => 'manage-laporan-kerja',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Menu Filters
    |--------------------------------------------------------------------------
    |
    | Here we can modify the menu filters of the admin panel.
    |
    | For detailed instructions you can look the menu filters section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Menu-Configuration
    |
    */

    'filters' => [
        JeroenNoten\LaravelAdminLte\Menu\Filters\GateFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\HrefFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\SearchFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ActiveFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\ClassesFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\LangFilter::class,
        JeroenNoten\LaravelAdminLte\Menu\Filters\DataFilter::class,
    ],

    /*
    |--------------------------------------------------------------------------
    | Plugins Initialization
    |--------------------------------------------------------------------------
    |
    | Here we can modify the plugins used inside the admin panel.
    |
    | For detailed instructions you can look the plugins section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Plugins-Configuration
    |
    */

    'plugins' => [
        'Datatables' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/jquery.dataTables.min.js',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/js/dataTables.bootstrap4.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdn.datatables.net/1.10.19/css/dataTables.bootstrap4.min.css',
                ],
            ],
        ],
        'Select2' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/js/select2.min.js',
                ],
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/select2/4.0.3/css/select2.css',
                ],
            ],
        ],
        'Chartjs' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/Chart.js/2.7.0/Chart.bundle.min.js',
                ],
            ],
        ],
        'Sweetalert2' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdn.jsdelivr.net/npm/sweetalert2@8',
                ],
            ],
        ],
        'Pace' => [
            'active' => false,
            'files' => [
                [
                    'type' => 'css',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/themes/blue/pace-theme-center-radar.min.css',
                ],
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => '//cdnjs.cloudflare.com/ajax/libs/pace/1.0.2/pace.min.js',
                ],
            ],
        ],
        'CKEditor' => [
            'active' => true,
            'files' => [
                [
                    'type' => 'js',
                    'asset' => false,
                    'location' => 'https://cdn.ckeditor.com/ckeditor5/27.1.0/classic/ckeditor.js',
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | IFrame
    |--------------------------------------------------------------------------
    |
    | Here we change the IFrame mode configuration. Note these changes will
    | only apply to the view that extends and enable the IFrame mode.
    |
    | For detailed instructions you can look the iframe mode section here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/IFrame-Mode-Configuration
    |
    */

    'iframe' => [
        'default_tab' => [
            'url' => null,
            'title' => null,
        ],
        'buttons' => [
            'close' => true,
            'close_all' => true,
            'close_all_other' => true,
            'scroll_left' => true,
            'scroll_right' => true,
            'fullscreen' => true,
        ],
        'options' => [
            'loading_screen' => 1000,
            'auto_show_new_tab' => true,
            'use_navbar_items' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Livewire
    |--------------------------------------------------------------------------
    |
    | Here we can enable the Livewire support.
    |
    | For detailed instructions you can look the livewire here:
    | https://github.com/jeroennoten/Laravel-AdminLTE/wiki/Other-Configuration
    |
    */

    'livewire' => false,
];































